{"name": "reza.unraid.api", "description": "Project for learning Unraid API and GraphQL", "type": "module", "private": true, "devDependencies": {"@sveltejs/adapter-auto": "^6.1.0", "@sveltejs/kit": "^2.28.0", "@types/bun": "latest", "svelte": "^5.38.1", "tailwindcss": "^4.1.11", "vite": "^7.1.2"}, "peerDependencies": {"typescript": "^5"}, "dependencies": {"@hono/node-server": "^1.18.1", "graphql-request": "^7.2.0", "hono": "^4.9.1", "jose": "^6.0.12", "postgres": "^3.4.7", "zod": "^4.0.17"}}