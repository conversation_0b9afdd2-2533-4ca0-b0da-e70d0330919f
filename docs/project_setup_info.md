# Unraid API Project Setup Information

### Purpose
- To learn how GraphQL works especially with Unraids API
- To leverage unraids API to build a custom GUI to do commands such as, view docker containers, start / restart them etc
- To learn new frameworks such as Hon<PERSON>, SvelteKit, Keycloak, PostgresSQL
- To deploy Key<PERSON>loak and this monorepo to my TrueNAS server
- To setup cloudflare tunnels on my `rezaunraid.net` domain to access this GUI via Nginx etc

### Tech Stack
| Category        | Technology           | Role                                                                                                                                                                                                 |
| :-------------- | :------------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Monorepo Tool** | Bun Workspaces       | Manages multiple packages (Hono backend, SvelteKit frontend) within a single repository, leveraging Bun's native workspace support for efficient dependency management and script execution.           |
| **Frontend**    | SvelteKit            | Provides the modern, snappy GUI for your Unraid Control Panel. Handles routing, data display, and user interaction. Built with Bun.                                                                  |
| **Backend API** | Hono                 | The lightweight, ultra-fast API gateway running on Bun. It will serve your SvelteKit application (if opting for SSR/SSG), proxy requests to Unraid's GraphQL API, and enforce Keycloak authentication. |
| **Auth Provider** | Keycloak             | The OpenID Connect/OAuth2 server that handles user authentication, authorization, and manages roles.                                                                                                 |
| **Database**    | PostgreSQL           | The robust relational database used by Keycloak to store its configuration, realms, users, and session data.                                                                                         |
| **GraphQL Client** | `graphql-request`   | Used within your Hono backend to send queries and mutations to Unraid's GraphQL API.                                                                                                               |
| **UI Components** | Shadcn/ui + Tailwind CSS | Provides a consistent, beautiful, and highly customizable component library for your SvelteKit frontend, accelerating UI development.                                                              |
| **Runtime/Tooling** | Bun                  | The primary JavaScript runtime, package manager, and bundler for both your SvelteKit and Hono projects, offering superior speed and an integrated developer experience.                               |

### File Structure
proposed file structure for this monorepo