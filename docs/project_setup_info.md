# Unraid API Project Setup Information

### Purpose
- To learn how GraphQL works especially with Unraids API
- To leverage unraids API to build a custom GUI to do commands such as, view docker containers, start / restart them etc
- To learn new frameworks such as Hon<PERSON>, SvelteKit, Keycloak, PostgresSQL
- To deploy Key<PERSON>loak and this monorepo to my TrueNAS server
- To setup cloudflare tunnels on my `rezaunraid.net` domain to access this GUI via Nginx etc

### Tech Stack
| Category        | Technology           | Role                                                                                                                                                                                                 |
| :-------------- | :------------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Monorepo Tool** | Bun Workspaces       | Manages multiple packages (Hono backend, SvelteKit frontend) within a single repository, leveraging Bun's native workspace support for efficient dependency management and script execution.           |
| **Frontend**    | SvelteKit            | Provides the modern, snappy GUI for your Unraid Control Panel. Handles routing, data display, and user interaction. Built with Bun.                                                                  |
| **Backend API** | Hono                 | The lightweight, ultra-fast API gateway running on Bun. It will serve your SvelteKit application (if opting for SSR/SSG), proxy requests to Unraid's GraphQL API, and enforce Keycloak authentication. |
| **Auth Provider** | Keycloak             | The OpenID Connect/OAuth2 server that handles user authentication, authorization, and manages roles.                                                                                                 |
| **Database**    | PostgreSQL           | The robust relational database used by Keycloak to store its configuration, realms, users, and session data.                                                                                         |
| **GraphQL Client** | `graphql-request`   | Used within your Hono backend to send queries and mutations to Unraid's GraphQL API.                                                                                                               |
| **UI Components** | Shadcn/ui + Tailwind CSS | Provides a consistent, beautiful, and highly customizable component library for your SvelteKit frontend, accelerating UI development.                                                              |
| **Runtime/Tooling** | Bun                  | The primary JavaScript runtime, package manager, and bundler for both your SvelteKit and Hono projects, offering superior speed and an integrated developer experience.                               |

### Proposed File Structure

```
reza.unraid.api/
├── README.md                           # Project overview and setup instructions
├── package.json                        # Root package.json with Bun workspaces
├── bun.lockb                          # Bun lock file
├── .env.example                       # Environment variables template
├── .env                               # Environment variables (gitignored)
├── .gitignore                         # Git ignore rules
├── docker-compose.yml                 # Local development services
├── docker-compose.prod.yml            # Production deployment
├── nginx.conf                         # Nginx configuration for reverse proxy
├──
├── docs/                              # Documentation
│   ├── project_setup_info.md         # Project overview and tech stack
│   ├── 1-unraid-api-cli-commands.md  # Unraid API CLI reference
│   ├── 2-hono-monorepo-setup.md      # Hono and monorepo setup guide
│   ├── 3-keycloak-truenas-setup.md   # Keycloak deployment guide
│   ├── 4-docker-network-truenas.md   # Docker networking guide
│   ├── 5-postgresql-guide.md         # PostgreSQL setup and usage
│   ├── 6-sveltekit-guide.md          # SvelteKit frontend guide
│   ├── deployment.md                 # Deployment instructions
│   └── api-reference.md              # API documentation
│
├── server/                            # Hono backend API
│   ├── package.json
│   ├── tsconfig.json
│   ├── Dockerfile
│   ├── .env.example
│   ├── src/
│   │   ├── index.ts                   # Main server entry point
│   │   ├── config/                    # Configuration files
│   │   │   ├── database.ts            # Database connection config
│   │   │   ├── auth.ts                # Authentication config
│   │   │   ├── unraid.ts              # Unraid API config
│   │   │   └── cors.ts                # CORS configuration
│   │   ├── middleware/                # Custom middleware
│   │   │   ├── auth.ts                # JWT verification middleware
│   │   │   ├── cors.ts                # CORS middleware
│   │   │   ├── rate-limit.ts          # Rate limiting
│   │   │   ├── logging.ts             # Request logging
│   │   │   └── error-handler.ts       # Global error handling
│   │   ├── routes/                    # API route handlers
│   │   │   ├── index.ts               # Route registration
│   │   │   ├── auth.ts                # Authentication routes
│   │   │   ├── docker.ts              # Docker container management
│   │   │   ├── system.ts              # System information
│   │   │   ├── array.ts               # Array management
│   │   │   ├── users.ts               # User management
│   │   │   └── health.ts              # Health check endpoints
│   │   ├── services/                  # Business logic services
│   │   │   ├── database.ts            # Database service layer
│   │   │   ├── unraid-api.ts          # Unraid GraphQL client
│   │   │   ├── auth.ts                # Authentication service
│   │   │   ├── docker.ts              # Docker service
│   │   │   ├── system.ts              # System monitoring service
│   │   │   └── audit.ts               # Audit logging service
│   │   ├── types/                     # TypeScript type definitions
│   │   │   ├── api.ts                 # API response types
│   │   │   ├── auth.ts                # Authentication types
│   │   │   ├── docker.ts              # Docker types
│   │   │   ├── system.ts              # System types
│   │   │   └── database.ts            # Database types
│   │   └── utils/                     # Utility functions
│   │       ├── logger.ts              # Logging utilities
│   │       ├── validation.ts          # Input validation
│   │       ├── crypto.ts              # Cryptographic utilities
│   │       └── helpers.ts             # General helper functions
│   └── tests/                         # Backend tests
│       ├── unit/                      # Unit tests
│       ├── integration/               # Integration tests
│       └── fixtures/                  # Test fixtures
│
├── client/                            # SvelteKit frontend
│   ├── package.json
│   ├── tsconfig.json
│   ├── vite.config.ts
│   ├── svelte.config.js
│   ├── tailwind.config.js
│   ├── Dockerfile
│   ├── .env.example
│   ├── src/
│   │   ├── app.html                   # HTML template
│   │   ├── app.d.ts                   # Global type definitions
│   │   ├── hooks.client.ts            # Client-side hooks
│   │   ├── hooks.server.ts            # Server-side hooks (auth)
│   │   ├── lib/                       # Shared utilities and components
│   │   │   ├── components/            # Reusable components
│   │   │   │   ├── ui/                # Base UI components (shadcn-style)
│   │   │   │   │   ├── button.svelte
│   │   │   │   │   ├── card.svelte
│   │   │   │   │   ├── input.svelte
│   │   │   │   │   ├── modal.svelte
│   │   │   │   │   ├── table.svelte
│   │   │   │   │   └── toast.svelte
│   │   │   │   ├── layout/            # Layout components
│   │   │   │   │   ├── Header.svelte
│   │   │   │   │   ├── Sidebar.svelte
│   │   │   │   │   ├── Footer.svelte
│   │   │   │   │   └── Navigation.svelte
│   │   │   │   └── features/          # Feature-specific components
│   │   │   │       ├── docker/        # Docker management components
│   │   │   │       │   ├── ContainerCard.svelte
│   │   │   │       │   ├── ContainerList.svelte
│   │   │   │       │   ├── ContainerActions.svelte
│   │   │   │       │   └── ContainerLogs.svelte
│   │   │   │       ├── system/        # System monitoring components
│   │   │   │       │   ├── SystemStats.svelte
│   │   │   │       │   ├── DiskUsage.svelte
│   │   │   │       │   ├── MemoryUsage.svelte
│   │   │   │       │   └── CpuUsage.svelte
│   │   │   │       └── auth/          # Authentication components
│   │   │   │           ├── LoginForm.svelte
│   │   │   │           ├── UserProfile.svelte
│   │   │   │           └── ProtectedRoute.svelte
│   │   │   ├── stores/                # Svelte stores
│   │   │   │   ├── auth.ts            # Authentication store
│   │   │   │   ├── containers.ts      # Docker containers store
│   │   │   │   ├── system.ts          # System information store
│   │   │   │   ├── notifications.ts   # Toast notifications
│   │   │   │   └── theme.ts           # Theme/dark mode store
│   │   │   ├── api/                   # API client functions
│   │   │   │   ├── client.ts          # Base API client
│   │   │   │   ├── auth.ts            # Authentication API
│   │   │   │   ├── docker.ts          # Docker API calls
│   │   │   │   ├── system.ts          # System API calls
│   │   │   │   └── types.ts           # API type definitions
│   │   │   ├── utils/                 # Utility functions
│   │   │   │   ├── formatters.ts      # Data formatting utilities
│   │   │   │   ├── validators.ts      # Form validation
│   │   │   │   ├── constants.ts       # Application constants
│   │   │   │   └── helpers.ts         # General helpers
│   │   │   └── types/                 # TypeScript types
│   │   │       ├── auth.ts            # Authentication types
│   │   │       ├── docker.ts          # Docker types
│   │   │       ├── system.ts          # System types
│   │   │       └── ui.ts              # UI component types
│   │   ├── routes/                    # File-based routing
│   │   │   ├── +layout.svelte         # Root layout
│   │   │   ├── +layout.ts             # Root layout data
│   │   │   ├── +page.svelte           # Home page
│   │   │   ├── +page.ts               # Home page data
│   │   │   ├── auth/                  # Authentication routes
│   │   │   │   ├── login/
│   │   │   │   │   ├── +page.svelte
│   │   │   │   │   └── +page.ts
│   │   │   │   ├── callback/
│   │   │   │   │   ├── +page.svelte
│   │   │   │   │   └── +page.ts
│   │   │   │   └── logout/
│   │   │   │       └── +page.ts
│   │   │   ├── dashboard/             # Main dashboard
│   │   │   │   ├── +layout.svelte     # Dashboard layout
│   │   │   │   ├── +layout.ts         # Dashboard layout data
│   │   │   │   ├── +page.svelte       # Dashboard home
│   │   │   │   ├── containers/        # Container management
│   │   │   │   │   ├── +page.svelte
│   │   │   │   │   ├── +page.ts
│   │   │   │   │   └── [id]/          # Individual container
│   │   │   │   │       ├── +page.svelte
│   │   │   │   │       └── +page.ts
│   │   │   │   ├── system/            # System monitoring
│   │   │   │   │   ├── +page.svelte
│   │   │   │   │   ├── +page.ts
│   │   │   │   │   ├── disks/
│   │   │   │   │   │   ├── +page.svelte
│   │   │   │   │   │   └── +page.ts
│   │   │   │   │   └── logs/
│   │   │   │   │       ├── +page.svelte
│   │   │   │   │       └── +page.ts
│   │   │   │   └── settings/          # Application settings
│   │   │   │       ├── +page.svelte
│   │   │   │       ├── +page.ts
│   │   │   │       ├── profile/
│   │   │   │       │   ├── +page.svelte
│   │   │   │       │   └── +page.ts
│   │   │   │       └── api-keys/
│   │   │   │           ├── +page.svelte
│   │   │   │           └── +page.ts
│   │   │   └── api/                   # API routes (if needed)
│   │   │       └── health/
│   │   │           └── +server.ts
│   │   └── styles/                    # Global styles
│   │       ├── app.css                # Main stylesheet
│   │       ├── components.css         # Component styles
│   │       └── themes/                # Theme definitions
│   │           ├── light.css
│   │           └── dark.css
│   ├── static/                        # Static assets
│   │   ├── favicon.ico
│   │   ├── logo.svg
│   │   └── images/
│   └── tests/                         # Frontend tests
│       ├── unit/                      # Unit tests
│       ├── integration/               # Integration tests
│       └── e2e/                       # End-to-end tests
│
├── shared/                            # Shared types and utilities
│   ├── package.json
│   ├── tsconfig.json
│   ├── src/
│   │   ├── index.ts                   # Main exports
│   │   ├── types/                     # Shared TypeScript types
│   │   │   ├── api.ts                 # API types
│   │   │   ├── auth.ts                # Authentication types
│   │   │   ├── docker.ts              # Docker types
│   │   │   ├── system.ts              # System types
│   │   │   └── database.ts            # Database types
│   │   ├── utils/                     # Shared utilities
│   │   │   ├── validation.ts          # Validation schemas (Zod)
│   │   │   ├── constants.ts           # Shared constants
│   │   │   └── formatters.ts          # Data formatters
│   │   └── schemas/                   # Validation schemas
│   │       ├── auth.ts                # Auth validation schemas
│   │       ├── docker.ts              # Docker validation schemas
│   │       └── system.ts              # System validation schemas
│   └── tests/                         # Shared package tests
│
├── infrastructure/                    # Infrastructure and deployment
│   ├── docker/                        # Docker configurations
│   │   ├── Dockerfile.server          # Server Dockerfile
│   │   ├── Dockerfile.client          # Client Dockerfile
│   │   ├── docker-compose.dev.yml     # Development compose
│   │   ├── docker-compose.prod.yml    # Production compose
│   │   └── nginx/                     # Nginx configuration
│   │       ├── nginx.conf
│   │       ├── ssl/                   # SSL certificates
│   │       └── sites-available/
│   ├── kubernetes/                    # Kubernetes manifests (optional)
│   │   ├── namespace.yaml
│   │   ├── configmap.yaml
│   │   ├── secrets.yaml
│   │   ├── deployments/
│   │   ├── services/
│   │   └── ingress/
│   ├── terraform/                     # Infrastructure as code (optional)
│   │   ├── main.tf
│   │   ├── variables.tf
│   │   └── outputs.tf
│   └── scripts/                       # Deployment and utility scripts
│       ├── deploy.sh                  # Deployment script
│       ├── backup.sh                  # Backup script
│       ├── setup-dev.sh               # Development setup
│       └── health-check.sh            # Health monitoring
│
├── database/                          # Database related files
│   ├── migrations/                    # Database migrations
│   │   ├── 001_initial_schema.sql
│   │   ├── 002_add_audit_logs.sql
│   │   └── 003_add_indexes.sql
│   ├── seeds/                         # Database seed data
│   │   ├── users.sql
│   │   └── test_data.sql
│   └── backups/                       # Database backups
│
└── tools/                             # Development tools and scripts
    ├── scripts/                       # Build and utility scripts
    │   ├── build.sh                   # Build script
    │   ├── test.sh                    # Test runner
    │   ├── lint.sh                    # Linting script
    │   └── generate-types.sh          # Type generation
    ├── configs/                       # Tool configurations
    │   ├── eslint.config.js           # ESLint configuration
    │   ├── prettier.config.js         # Prettier configuration
    │   └── tsconfig.base.json         # Base TypeScript config
    └── templates/                     # Code templates
        ├── component.svelte.template
        ├── route.ts.template
        └── service.ts.template
```