# TrueNAS Networking Setup for Unraid API Project

## Overview
This guide covers the complete networking setup for your Unraid API project on TrueNAS, including Docker network creation, firewall configuration, and network conflict resolution.

## Current Network Analysis

### Existing Networks Check
First, let's check what networks already exist to avoid conflicts:

```bash
# Check existing Docker networks
docker network ls

# Inspect existing Introspection network
docker network inspect introspection-prod-network

# Check all network subnets in use
docker network ls -q | xargs docker network inspect | grep -E '"Subnet"|"Name"'

# Check TrueNAS network interfaces
ip addr show

# Check routing table
ip route show
```

### Your Existing Network
Based on your output, you have:
- **Network**: `introspection-prod-network`
- **Subnet**: `**********/24`
- **Gateway**: `**********`
- **IPv6**: `fdd0:0:0:7::/64`

## Recommended Network Configuration for Unraid API

### Network Subnet Selection
To avoid conflicts with your existing `**********/24` network, we'll use:
- **Subnet**: `**********/24` (next available in the 172.30.x.x range)
- **Gateway**: `**********`
- **IP Range**: `**********` - `**********54`

### Create Unraid API Network

```bash
# Create the unraid-api network
docker network create \
  --driver bridge \
  --subnet=**********/24 \
  --gateway=********** \
  --ip-range=**********/24 \
  --label project=unraid-api \
  --label environment=production \
  unraid-api-network

# Verify network creation
docker network inspect unraid-api-network
```

### Network Verification Script

Create this script to check network health:

```bash
#!/bin/bash
# check-networks.sh

echo "=== Docker Network Analysis ==="
echo

echo "1. All Docker Networks:"
docker network ls
echo

echo "2. Introspection Network Details:"
docker network inspect introspection-prod-network --format='{{.Name}}: {{range .IPAM.Config}}{{.Subnet}}{{end}}'
echo

echo "3. Unraid API Network Details (if exists):"
if docker network inspect unraid-api-network >/dev/null 2>&1; then
    docker network inspect unraid-api-network --format='{{.Name}}: {{range .IPAM.Config}}{{.Subnet}}{{end}}'
else
    echo "unraid-api-network does not exist"
fi
echo

echo "4. Network Subnet Conflicts Check:"
echo "Checking for subnet overlaps..."

# Get all subnets
SUBNETS=$(docker network ls -q | xargs docker network inspect | jq -r '.[].IPAM.Config[]?.Subnet' 2>/dev/null | grep -v null)

echo "Current subnets in use:"
echo "$SUBNETS"
echo

echo "5. TrueNAS Host Network Interfaces:"
ip addr show | grep -E "inet |^[0-9]+:"
echo

echo "6. Routing Table:"
ip route show
echo

echo "7. Container Network Assignments:"
for network in $(docker network ls --format "{{.Name}}" | grep -v bridge | grep -v host | grep -v none); do
    echo "Network: $network"
    docker network inspect $network --format='{{range .Containers}}  {{.Name}}: {{.IPv4Address}}{{"\n"}}{{end}}'
done
```

## TrueNAS Firewall Configuration

### Check Current Firewall Rules
```bash
# Check if UFW is enabled
sudo ufw status

# Check iptables rules
sudo iptables -L -n

# Check Docker iptables rules
sudo iptables -L DOCKER -n
sudo iptables -L DOCKER-USER -n
```

### Configure Firewall for Unraid API

```bash
# Allow communication within unraid-api network
sudo iptables -A DOCKER-USER -s **********/24 -d **********/24 -j ACCEPT

# Allow communication between unraid-api and introspection networks (if needed)
sudo iptables -A DOCKER-USER -s **********/24 -d **********/24 -j ACCEPT
sudo iptables -A DOCKER-USER -s **********/24 -d **********/24 -j ACCEPT

# Allow access to Unraid server from unraid-api network
# Replace ************* with your actual Unraid server IP
sudo iptables -A DOCKER-USER -s **********/24 -d ************* -j ACCEPT

# Allow specific ports from unraid-api network to host
sudo iptables -A DOCKER-USER -s **********/24 -d ***********/24 -p tcp --dport 80 -j ACCEPT
sudo iptables -A DOCKER-USER -s **********/24 -d ***********/24 -p tcp --dport 443 -j ACCEPT
sudo iptables -A DOCKER-USER -s **********/24 -d ***********/24 -p tcp --dport 22 -j ACCEPT

# Save iptables rules (method varies by system)
# For TrueNAS/FreeBSD-based systems:
sudo service netif restart
```

### Firewall Management Script

```bash
#!/bin/bash
# manage-firewall.sh

ACTION=$1

case $ACTION in
    "status")
        echo "=== Firewall Status ==="
        echo "UFW Status:"
        sudo ufw status
        echo
        echo "Docker iptables rules:"
        sudo iptables -L DOCKER-USER -n --line-numbers
        ;;
    
    "setup")
        echo "Setting up firewall rules for Unraid API..."
        
        # Create DOCKER-USER chain if it doesn't exist
        sudo iptables -N DOCKER-USER 2>/dev/null || true
        
        # Allow unraid-api network internal communication
        sudo iptables -A DOCKER-USER -s **********/24 -d **********/24 -j ACCEPT
        
        # Allow access to Unraid server (replace with your Unraid IP)
        read -p "Enter your Unraid server IP (e.g., *************): " UNRAID_IP
        sudo iptables -A DOCKER-USER -s **********/24 -d $UNRAID_IP -j ACCEPT
        
        echo "Firewall rules added successfully"
        ;;
    
    "remove")
        echo "Removing Unraid API firewall rules..."
        # Remove rules (you'll need to identify line numbers first)
        sudo iptables -L DOCKER-USER -n --line-numbers
        echo "Use 'sudo iptables -D DOCKER-USER <line_number>' to remove specific rules"
        ;;
    
    *)
        echo "Usage: $0 {status|setup|remove}"
        echo "  status - Show current firewall status"
        echo "  setup  - Add firewall rules for Unraid API"
        echo "  remove - Remove Unraid API firewall rules"
        ;;
esac
```

## Docker Compose Network Configuration

### Updated docker-compose.yml with Correct Network

```yaml
version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: unraid-postgres
    environment:
      POSTGRES_DB: unraid_db
      POSTGRES_USER: unraid_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      unraid-api-network:
        ipv4_address: **********0
    restart: unless-stopped

  keycloak:
    image: quay.io/keycloak/keycloak:23.0
    container_name: unraid-keycloak
    environment:
      KC_DB: postgres
      KC_DB_URL: *****************************************
      KC_DB_USERNAME: unraid_user
      KC_DB_PASSWORD: ${POSTGRES_PASSWORD}
      KEYCLOAK_ADMIN: admin
      KEYCLOAK_ADMIN_PASSWORD: ${KEYCLOAK_ADMIN_PASSWORD}
      KC_HOSTNAME: auth.rezaunraid.net
      KC_PROXY: edge
      KC_HTTP_ENABLED: true
    ports:
      - "8080:8080"
    networks:
      unraid-api-network:
        ipv4_address: **********1
    depends_on:
      - postgres
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: unraid-redis
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    networks:
      unraid-api-network:
        ipv4_address: **********2
    restart: unless-stopped

  unraid-api-server:
    build: ./server
    container_name: unraid-api-server
    environment:
      DATABASE_URL: postgresql://unraid_user:${POSTGRES_PASSWORD}@postgres:5432/unraid_db
      REDIS_URL: redis://:${REDIS_PASSWORD}@redis:6379
      UNRAID_HOST: ${UNRAID_HOST}
      UNRAID_API_KEY: ${UNRAID_API_KEY}
    ports:
      - "3001:3001"
    networks:
      unraid-api-network:
        ipv4_address: ***********
    depends_on:
      - postgres
      - redis
    restart: unless-stopped

  unraid-api-client:
    build: ./client
    container_name: unraid-api-client
    environment:
      PUBLIC_API_URL: http://unraid-api-server:3001
    ports:
      - "3000:3000"
    networks:
      unraid-api-network:
        ipv4_address: ***********
    depends_on:
      - unraid-api-server
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    container_name: unraid-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    networks:
      unraid-api-network:
        ipv4_address: ***********
    depends_on:
      - unraid-api-client
      - keycloak
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  unraid-api-network:
    external: true
```

## Network Monitoring and Troubleshooting

### Network Health Check Script

```bash
#!/bin/bash
# network-health.sh

echo "=== Unraid API Network Health Check ==="
echo

# Check if network exists
if ! docker network inspect unraid-api-network >/dev/null 2>&1; then
    echo "❌ unraid-api-network does not exist!"
    echo "Run: docker network create --driver bridge --subnet=**********/24 --gateway=********** unraid-api-network"
    exit 1
fi

echo "✅ unraid-api-network exists"

# Check containers in network
echo
echo "Containers in unraid-api-network:"
docker network inspect unraid-api-network --format='{{range .Containers}}{{.Name}}: {{.IPv4Address}}{{"\n"}}{{end}}'

# Test connectivity between containers
echo
echo "Testing container connectivity..."

CONTAINERS=$(docker network inspect unraid-api-network --format='{{range .Containers}}{{.Name}} {{end}}')

for container in $CONTAINERS; do
    if docker exec $container ping -c 1 ********** >/dev/null 2>&1; then
        echo "✅ $container can reach gateway"
    else
        echo "❌ $container cannot reach gateway"
    fi
done

# Check external connectivity
echo
echo "Testing external connectivity..."
if docker run --rm --network unraid-api-network alpine ping -c 1 ******* >/dev/null 2>&1; then
    echo "✅ External connectivity working"
else
    echo "❌ External connectivity failed"
fi

# Check Unraid server connectivity
echo
echo "Testing Unraid server connectivity..."
read -p "Enter your Unraid server IP: " UNRAID_IP
if docker run --rm --network unraid-api-network alpine ping -c 1 $UNRAID_IP >/dev/null 2>&1; then
    echo "✅ Can reach Unraid server at $UNRAID_IP"
else
    echo "❌ Cannot reach Unraid server at $UNRAID_IP"
fi
```

## Environment Configuration

### Network Environment Variables

```env
# .env
# Network Configuration
DOCKER_NETWORK=unraid-api-network
NETWORK_SUBNET=**********/24
NETWORK_GATEWAY=**********

# Service IP Addresses
POSTGRES_IP=**********0
KEYCLOAK_IP=**********1
REDIS_IP=**********2
API_SERVER_IP=***********
API_CLIENT_IP=***********
NGINX_IP=***********

# Unraid Configuration
UNRAID_HOST=*************
UNRAID_API_KEY=your_api_key_here

# Database Configuration
POSTGRES_PASSWORD=your_secure_password
REDIS_PASSWORD=your_redis_password
KEYCLOAK_ADMIN_PASSWORD=your_keycloak_password
```

## Cleanup and Maintenance

### Network Cleanup Script

```bash
#!/bin/bash
# cleanup-networks.sh

echo "=== Network Cleanup ==="

# Remove unused networks
echo "Removing unused networks..."
docker network prune -f

# Show remaining networks
echo "Remaining networks:"
docker network ls

# Check for orphaned containers
echo "Checking for containers not in any custom network..."
docker ps --format "table {{.Names}}\t{{.Networks}}" | grep bridge
```

## Troubleshooting Common Issues

### 1. Subnet Conflicts
```bash
# Check for conflicts
docker network ls -q | xargs docker network inspect | grep -E '"Subnet"|"Name"'

# If conflict exists, use different subnet like **********/24
```

### 2. Container Communication Issues
```bash
# Test container-to-container communication
docker exec container1 ping container2

# Check DNS resolution
docker exec container1 nslookup container2
```

### 3. External Access Issues
```bash
# Check firewall rules
sudo iptables -L DOCKER-USER -n

# Test external connectivity
docker run --rm --network unraid-api-network alpine ping -c 1 google.com
```

## Security Best Practices

1. **Network Isolation**: Keep unraid-api network separate from other projects
2. **Firewall Rules**: Only allow necessary traffic between networks
3. **IP Assignment**: Use static IP assignments for critical services
4. **Monitoring**: Regularly check network health and connectivity
5. **Documentation**: Keep network configuration documented and up-to-date
