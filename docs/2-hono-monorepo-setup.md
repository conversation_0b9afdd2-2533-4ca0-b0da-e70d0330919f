# Hono & Monorepo Setup Guide

## What is Hono?

Hono is a small, simple, and ultrafast web framework for the Edges. It works on any JavaScript runtime: Cloudflare Workers, Fastly Compute@Edge, Deno, Bun, Vercel, AWS Lambda, Lambda@Edge, and Node.js.

### Why Hono is Perfect for Your Project

1. **Ultra-fast**: Built for performance with minimal overhead
2. **TypeScript First**: Excellent TypeScript support out of the box
3. **Runtime Agnostic**: Works perfectly with Bun (your chosen runtime)
4. **Middleware Support**: Rich ecosystem of middleware
5. **Small Bundle Size**: Minimal footprint
6. **Modern API**: Clean, intuitive API design

## Monorepo Structure with Bun Workspaces

### Project Structure
```
reza.unraid.api/
├── package.json                 # Single package.json for entire project
├── bun.lockb                   # Bun lock file
├── tsconfig.json               # Base TypeScript configuration
├── docs/                       # Documentation
├── server/                     # Hono backend
│   ├── tsconfig.json          # Server TypeScript config (extends base)
│   ├── src/
│   │   ├── index.ts           # Main server entry
│   │   ├── routes/            # API routes
│   │   ├── middleware/        # Custom middleware
│   │   ├── services/          # Business logic
│   │   └── types/             # TypeScript types
│   └── Dockerfile
├── client/                     # SvelteKit frontend
│   ├── tsconfig.json          # Client TypeScript config (extends base)
│   ├── src/
│   │   ├── app.html
│   │   ├── routes/
│   │   ├── lib/
│   │   └── components/
│   ├── static/
│   ├── vite.config.ts
│   ├── svelte.config.js
│   ├── tailwind.config.js
│   └── Dockerfile
├── shared/                     # Shared types and utilities
│   ├── tsconfig.json          # Shared TypeScript config (extends base)
│   ├── src/
│   │   ├── types/
│   │   └── utils/
├── docker-compose.yml          # For local development
└── README.md
```

## Setting Up the Monorepo

### 1. Root Package.json Configuration
```json
{
  "name": "reza-unraid-api",
  "version": "1.0.0",
  "private": true,
  "type": "module",
  "scripts": {
    "dev": "concurrently \"bun run dev:server\" \"bun run dev:client\"",
    "dev:server": "bun run --cwd server dev",
    "dev:client": "bun run --cwd client dev",
    "build": "bun run build:shared && bun run build:server && bun run build:client",
    "build:server": "bun run --cwd server build",
    "build:client": "bun run --cwd client build",
    "build:shared": "bun run --cwd shared build",
    "start": "bun run --cwd server start",
    "test": "bun test",
    "test:server": "bun run --cwd server test",
    "test:client": "bun run --cwd client test",
    "lint": "eslint . --ext .ts,.js,.svelte",
    "format": "prettier --write .",
    "type-check": "tsc --noEmit && bun run --cwd server type-check && bun run --cwd client check"
  },
  "dependencies": {
    "hono": "^3.12.0",
    "@hono/node-server": "^1.8.0",
    "graphql-request": "^6.1.0",
    "jose": "^5.2.0",
    "zod": "^3.22.0",
    "postgres": "^3.4.0"
  },
  "devDependencies": {
    "@types/bun": "latest",
    "@types/pg": "^8.10.0",
    "typescript": "^5.0.0",
    "eslint": "^8.57.0",
    "@typescript-eslint/eslint-plugin": "^6.0.0",
    "@typescript-eslint/parser": "^6.0.0",
    "prettier": "^3.0.0",
    "concurrently": "^8.2.0"
  }
}
```

### 2. Base TypeScript Configuration
```json
// tsconfig.json (root)
{
  "compilerOptions": {
    "target": "ES2022",
    "module": "ESNext",
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "noEmit": true,
    "strict": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "baseUrl": ".",
    "paths": {
      "@shared/*": ["./shared/src/*"],
      "@server/*": ["./server/src/*"],
      "@client/*": ["./client/src/*"]
    }
  },
  "include": ["**/*.ts", "**/*.js"],
  "exclude": ["node_modules", "dist", "build"]
}
```

### 3. Server TypeScript Configuration
```json
// server/tsconfig.json
{
  "extends": "../tsconfig.json",
  "compilerOptions": {
    "outDir": "./dist",
    "rootDir": "./src",
    "noEmit": false,
    "types": ["bun-types"]
  },
  "include": ["src/**/*"],
  "exclude": ["dist", "tests"]
}
```

### 4. Server Setup (Hono)

#### server/src/index.ts
```typescript
import { Hono } from 'hono'
import { cors } from 'hono/cors'
import { logger } from 'hono/logger'
import { prettyJSON } from 'hono/pretty-json'
import { serve } from '@hono/node-server'

// Import routes
import { dockerRoutes } from './routes/docker'
import { authRoutes } from './routes/auth'
import { systemRoutes } from './routes/system'

const app = new Hono()

// Middleware
app.use('*', logger())
app.use('*', prettyJSON())
app.use('*', cors({
  origin: ['http://localhost:5173', 'http://localhost:3000'],
  credentials: true,
}))

// Health check
app.get('/health', (c) => {
  return c.json({ status: 'ok', timestamp: new Date().toISOString() })
})

// API routes
app.route('/api/docker', dockerRoutes)
app.route('/api/auth', authRoutes)
app.route('/api/system', systemRoutes)

// 404 handler
app.notFound((c) => {
  return c.json({ error: 'Not Found' }, 404)
})

// Error handler
app.onError((err, c) => {
  console.error('Server error:', err)
  return c.json({ error: 'Internal Server Error' }, 500)
})

const port = process.env.PORT || 3001

console.log(`🚀 Hono server starting on port ${port}`)

serve({
  fetch: app.fetch,
  port: Number(port),
})

export default app
```

#### server/src/routes/docker.ts
```typescript
import { Hono } from 'hono'
import { request } from 'graphql-request'
import { z } from 'zod'

const dockerRoutes = new Hono()

const UNRAID_GRAPHQL_ENDPOINT = process.env.UNRAID_GRAPHQL_ENDPOINT || 'http://*************/graphql'

// Get all containers
dockerRoutes.get('/', async (c) => {
  const query = `
    query GetDockers {
      dockers {
        name
        state
        image
        ports
        created
        status
      }
    }
  `
  
  try {
    const data = await request(UNRAID_GRAPHQL_ENDPOINT, query)
    return c.json(data)
  } catch (error) {
    console.error('Failed to fetch containers:', error)
    return c.json({ error: 'Failed to fetch containers' }, 500)
  }
})

// Start container
dockerRoutes.post('/:name/start', async (c) => {
  const name = c.req.param('name')
  
  const mutation = `
    mutation StartDocker($name: String!) {
      startDocker(name: $name) {
        success
        message
      }
    }
  `
  
  try {
    const data = await request(UNRAID_GRAPHQL_ENDPOINT, mutation, { name })
    return c.json(data)
  } catch (error) {
    console.error('Failed to start container:', error)
    return c.json({ error: 'Failed to start container' }, 500)
  }
})

// Stop container
dockerRoutes.post('/:name/stop', async (c) => {
  const name = c.req.param('name')
  
  const mutation = `
    mutation StopDocker($name: String!) {
      stopDocker(name: $name) {
        success
        message
      }
    }
  `
  
  try {
    const data = await request(UNRAID_GRAPHQL_ENDPOINT, mutation, { name })
    return c.json(data)
  } catch (error) {
    console.error('Failed to stop container:', error)
    return c.json({ error: 'Failed to stop container' }, 500)
  }
})

export { dockerRoutes }
```

### 5. Shared Package Setup

#### shared/tsconfig.json
```json
{
  "extends": "../tsconfig.json",
  "compilerOptions": {
    "outDir": "./dist",
    "rootDir": "./src",
    "noEmit": false,
    "declaration": true,
    "declarationMap": true
  },
  "include": ["src/**/*"],
  "exclude": ["dist"]
}
```

#### shared/src/types/index.ts
```typescript
import { z } from 'zod'

// Docker container types
export const DockerContainerSchema = z.object({
  name: z.string(),
  state: z.enum(['running', 'stopped', 'paused']),
  image: z.string(),
  ports: z.array(z.string()),
  created: z.string(),
  status: z.string(),
})

export type DockerContainer = z.infer<typeof DockerContainerSchema>

// API response types
export const ApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string().optional(),
  data: z.any().optional(),
})

export type ApiResponse<T = any> = {
  success: boolean
  message?: string
  data?: T
}

// System info types
export const SystemInfoSchema = z.object({
  version: z.string(),
  uptime: z.number(),
  memory: z.object({
    total: z.number(),
    used: z.number(),
    free: z.number(),
  }),
  cpu: z.object({
    usage: z.number(),
    temperature: z.number(),
  }),
})

export type SystemInfo = z.infer<typeof SystemInfoSchema>
```

## Development Commands

### Setup Commands
```bash
# Install all dependencies
bun install

# Add dependencies to the project
bun add hono @hono/node-server graphql-request jose zod postgres

# Add dev dependencies
bun add -d typescript @types/bun @types/pg eslint prettier concurrently

# Add SvelteKit dependencies (when setting up client)
bun add -d @sveltejs/kit @sveltejs/adapter-auto vite svelte

# Add Tailwind CSS
bun add -d tailwindcss @tailwindcss/typography @tailwindcss/forms autoprefixer postcss
```

### Development Commands
```bash
# Start both server and client in development mode
bun run dev

# Start only the server
bun run dev:server

# Start only the client
bun run dev:client

# Build everything
bun run build

# Build specific parts
bun run build:server
bun run build:client
bun run build:shared

# Run tests
bun run test

# Type checking
bun run type-check

# Linting and formatting
bun run lint
bun run format
```

## Environment Configuration

### .env.example
```env
# Server Configuration
PORT=3001
NODE_ENV=development

# Unraid Configuration
UNRAID_HOST=*************
UNRAID_GRAPHQL_ENDPOINT=http://*************/graphql
UNRAID_USERNAME=admin
UNRAID_PASSWORD=your_password

# Keycloak Configuration
KEYCLOAK_URL=http://localhost:8080
KEYCLOAK_REALM=unraid-realm
KEYCLOAK_CLIENT_ID=unraid-client
KEYCLOAK_CLIENT_SECRET=your_client_secret

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/unraid_db
```

## Initial Setup Commands (After `bun init`)

Since you've already run `bun init`, here are the commands to set up your project structure:

### 1. Install All Dependencies
```bash
# Core dependencies
bun add hono @hono/node-server graphql-request jose zod postgres

# SvelteKit dependencies
bun add -d @sveltejs/kit @sveltejs/adapter-auto vite svelte

# UI and styling
bun add -d tailwindcss @tailwindcss/typography @tailwindcss/forms autoprefixer postcss
bun add lucide-svelte

# Authentication
bun add @auth/sveltekit @auth/core

# Development tools
bun add -d typescript @types/bun @types/pg eslint prettier concurrently
bun add -d @typescript-eslint/eslint-plugin @typescript-eslint/parser
bun add -d eslint-config-prettier eslint-plugin-svelte prettier-plugin-svelte
bun add -d svelte-check tslib vitest
```

### 2. Create Directory Structure
```bash
# Create main directories
mkdir -p server/src/{config,middleware,routes,services,types,utils}
mkdir -p client/src/{lib/{components/{ui,layout,features},stores,api,utils,types},routes,styles}
mkdir -p shared/src/{types,utils,schemas}
mkdir -p docs infrastructure database tools

# Create subdirectories
mkdir -p client/src/lib/components/features/{docker,system,auth}
mkdir -p client/src/routes/{auth/{login,callback,logout},dashboard/{containers,system,settings}}
mkdir -p server/tests/{unit,integration,fixtures}
mkdir -p client/tests/{unit,integration,e2e}
```

### 3. Create Configuration Files
```bash
# Create TypeScript configs (content provided in sections above)
touch tsconfig.json
touch server/tsconfig.json
touch client/tsconfig.json
touch shared/tsconfig.json

# Create SvelteKit configs
touch client/svelte.config.js
touch client/vite.config.ts
touch client/tailwind.config.js

# Create environment files
touch .env.example
touch .env

# Create Docker files
touch server/Dockerfile
touch client/Dockerfile
touch docker-compose.yml
touch docker-compose.prod.yml
```

### 4. Initialize SvelteKit Structure
```bash
# Create essential SvelteKit files
touch client/src/app.html
touch client/src/app.d.ts
touch client/src/hooks.client.ts
touch client/src/hooks.server.ts

# Create basic routes
touch client/src/routes/+layout.svelte
touch client/src/routes/+layout.ts
touch client/src/routes/+page.svelte
touch client/src/routes/+page.ts

# Create static directory
mkdir -p client/static
```

### 5. No Additional Init Commands Needed!
Unlike traditional monorepos, you don't need to run:
- ❌ `npm create svelte@latest` (dependencies already in root package.json)
- ❌ `npm create hono@latest` (Hono is just a dependency)
- ❌ Multiple `npm init` commands (single package.json approach)

You just need to create the files and configure them according to the examples in this guide.

## Next Steps

1. Copy the configuration examples from this guide into your files
2. Set up environment variables for Unraid API and Keycloak
3. Create your first Hono routes and SvelteKit pages
4. Set up Docker configuration for deployment
5. Configure Keycloak authentication