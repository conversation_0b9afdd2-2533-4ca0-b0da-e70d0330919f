# Hono & Monorepo Setup Guide

## What is Hono?

Hono is a small, simple, and ultrafast web framework for the Edges. It works on any JavaScript runtime: Cloudflare Workers, <PERSON>ly Compute@Edge, Deno, Bun, Vercel, AWS Lambda, Lambda@Edge, and Node.js.

### Why Hono is Perfect for Your Project

1. **Ultra-fast**: Built for performance with minimal overhead
2. **TypeScript First**: Excellent TypeScript support out of the box
3. **Runtime Agnostic**: Works perfectly with Bun (your chosen runtime)
4. **Middleware Support**: Rich ecosystem of middleware
5. **Small Bundle Size**: Minimal footprint
6. **Modern API**: Clean, intuitive API design

## Monorepo Structure with Bun Workspaces

### Project Structure
```
reza.unraid.api/
├── package.json                 # Root package.json with workspaces
├── bun.lockb                   # Bun lock file
├── docs/                       # Documentation
├── packages/
│   ├── server/                 # Hono backend
│   │   ├── package.json
│   │   ├── src/
│   │   │   ├── index.ts        # Main server entry
│   │   │   ├── routes/         # API routes
│   │   │   ├── middleware/     # Custom middleware
│   │   │   ├── services/       # Business logic
│   │   │   └── types/          # TypeScript types
│   │   └── tsconfig.json
│   ├── client/                 # SvelteKit frontend
│   │   ├── package.json
│   │   ├── src/
│   │   │   ├── app.html
│   │   │   ├── routes/
│   │   │   ├── lib/
│   │   │   └── components/
│   │   ├── static/
│   │   ├── vite.config.ts
│   │   └── tsconfig.json
│   └── shared/                 # Shared types and utilities
│       ├── package.json
│       ├── src/
│       │   ├── types/
│       │   └── utils/
│       └── tsconfig.json
├── docker-compose.yml          # For local development
└── README.md
```

## Setting Up the Monorepo

### 1. Initialize Root Package.json
```json
{
  "name": "reza-unraid-api",
  "version": "1.0.0",
  "private": true,
  "workspaces": [
    "packages/*"
  ],
  "scripts": {
    "dev": "bun run --filter='*' dev",
    "build": "bun run --filter='*' build",
    "start": "bun run --filter=server start",
    "test": "bun run --filter='*' test",
    "lint": "bun run --filter='*' lint",
    "type-check": "bun run --filter='*' type-check"
  },
  "devDependencies": {
    "@types/bun": "latest",
    "typescript": "^5.0.0"
  }
}
```

### 2. Server Package Setup (Hono)

#### packages/server/package.json
```json
{
  "name": "@reza-unraid/server",
  "version": "1.0.0",
  "scripts": {
    "dev": "bun run --watch src/index.ts",
    "build": "bun build src/index.ts --outdir dist --target bun",
    "start": "bun run dist/index.js",
    "test": "bun test",
    "lint": "eslint src/**/*.ts",
    "type-check": "tsc --noEmit"
  },
  "dependencies": {
    "hono": "^3.12.0",
    "@hono/node-server": "^1.8.0",
    "graphql-request": "^6.1.0",
    "jose": "^5.2.0",
    "zod": "^3.22.0",
    "@reza-unraid/shared": "workspace:*"
  },
  "devDependencies": {
    "@types/bun": "latest",
    "typescript": "^5.0.0"
  }
}
```

#### packages/server/src/index.ts
```typescript
import { Hono } from 'hono'
import { cors } from 'hono/cors'
import { logger } from 'hono/logger'
import { prettyJSON } from 'hono/pretty-json'
import { serve } from '@hono/node-server'

// Import routes
import { dockerRoutes } from './routes/docker'
import { authRoutes } from './routes/auth'
import { systemRoutes } from './routes/system'

const app = new Hono()

// Middleware
app.use('*', logger())
app.use('*', prettyJSON())
app.use('*', cors({
  origin: ['http://localhost:5173', 'http://localhost:3000'],
  credentials: true,
}))

// Health check
app.get('/health', (c) => {
  return c.json({ status: 'ok', timestamp: new Date().toISOString() })
})

// API routes
app.route('/api/docker', dockerRoutes)
app.route('/api/auth', authRoutes)
app.route('/api/system', systemRoutes)

// 404 handler
app.notFound((c) => {
  return c.json({ error: 'Not Found' }, 404)
})

// Error handler
app.onError((err, c) => {
  console.error('Server error:', err)
  return c.json({ error: 'Internal Server Error' }, 500)
})

const port = process.env.PORT || 3001

console.log(`🚀 Hono server starting on port ${port}`)

serve({
  fetch: app.fetch,
  port: Number(port),
})

export default app
```

#### packages/server/src/routes/docker.ts
```typescript
import { Hono } from 'hono'
import { request } from 'graphql-request'
import { z } from 'zod'

const dockerRoutes = new Hono()

const UNRAID_GRAPHQL_ENDPOINT = process.env.UNRAID_GRAPHQL_ENDPOINT || 'http://*************/graphql'

// Get all containers
dockerRoutes.get('/', async (c) => {
  const query = `
    query GetDockers {
      dockers {
        name
        state
        image
        ports
        created
        status
      }
    }
  `
  
  try {
    const data = await request(UNRAID_GRAPHQL_ENDPOINT, query)
    return c.json(data)
  } catch (error) {
    console.error('Failed to fetch containers:', error)
    return c.json({ error: 'Failed to fetch containers' }, 500)
  }
})

// Start container
dockerRoutes.post('/:name/start', async (c) => {
  const name = c.req.param('name')
  
  const mutation = `
    mutation StartDocker($name: String!) {
      startDocker(name: $name) {
        success
        message
      }
    }
  `
  
  try {
    const data = await request(UNRAID_GRAPHQL_ENDPOINT, mutation, { name })
    return c.json(data)
  } catch (error) {
    console.error('Failed to start container:', error)
    return c.json({ error: 'Failed to start container' }, 500)
  }
})

// Stop container
dockerRoutes.post('/:name/stop', async (c) => {
  const name = c.req.param('name')
  
  const mutation = `
    mutation StopDocker($name: String!) {
      stopDocker(name: $name) {
        success
        message
      }
    }
  `
  
  try {
    const data = await request(UNRAID_GRAPHQL_ENDPOINT, mutation, { name })
    return c.json(data)
  } catch (error) {
    console.error('Failed to stop container:', error)
    return c.json({ error: 'Failed to stop container' }, 500)
  }
})

export { dockerRoutes }
```

### 3. Shared Package Setup

#### packages/shared/package.json
```json
{
  "name": "@reza-unraid/shared",
  "version": "1.0.0",
  "main": "dist/index.js",
  "types": "dist/index.d.ts",
  "scripts": {
    "build": "tsc",
    "dev": "tsc --watch",
    "type-check": "tsc --noEmit"
  },
  "dependencies": {
    "zod": "^3.22.0"
  },
  "devDependencies": {
    "typescript": "^5.0.0"
  }
}
```

#### packages/shared/src/types/index.ts
```typescript
import { z } from 'zod'

// Docker container types
export const DockerContainerSchema = z.object({
  name: z.string(),
  state: z.enum(['running', 'stopped', 'paused']),
  image: z.string(),
  ports: z.array(z.string()),
  created: z.string(),
  status: z.string(),
})

export type DockerContainer = z.infer<typeof DockerContainerSchema>

// API response types
export const ApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string().optional(),
  data: z.any().optional(),
})

export type ApiResponse<T = any> = {
  success: boolean
  message?: string
  data?: T
}

// System info types
export const SystemInfoSchema = z.object({
  version: z.string(),
  uptime: z.number(),
  memory: z.object({
    total: z.number(),
    used: z.number(),
    free: z.number(),
  }),
  cpu: z.object({
    usage: z.number(),
    temperature: z.number(),
  }),
})

export type SystemInfo = z.infer<typeof SystemInfoSchema>
```

## Development Commands

### Setup Commands
```bash
# Initialize the project
bun install

# Install dependencies for all packages
bun install --frozen-lockfile

# Add a dependency to a specific package
bun add hono --filter=server
bun add svelte --filter=client

# Add a dev dependency
bun add -d typescript --filter=server
```

### Development Commands
```bash
# Start all packages in development mode
bun run dev

# Start only the server
bun run --filter=server dev

# Start only the client
bun run --filter=client dev

# Build all packages
bun run build

# Run tests
bun run test

# Type checking
bun run type-check
```

## Environment Configuration

### .env.example
```env
# Server Configuration
PORT=3001
NODE_ENV=development

# Unraid Configuration
UNRAID_HOST=*************
UNRAID_GRAPHQL_ENDPOINT=http://*************/graphql
UNRAID_USERNAME=admin
UNRAID_PASSWORD=your_password

# Keycloak Configuration
KEYCLOAK_URL=http://localhost:8080
KEYCLOAK_REALM=unraid-realm
KEYCLOAK_CLIENT_ID=unraid-client
KEYCLOAK_CLIENT_SECRET=your_client_secret

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/unraid_db
```

## Next Steps

1. Set up the client package with SvelteKit
2. Configure TypeScript for the entire monorepo
3. Set up ESLint and Prettier
4. Create Docker configuration for deployment
5. Set up CI/CD pipeline
