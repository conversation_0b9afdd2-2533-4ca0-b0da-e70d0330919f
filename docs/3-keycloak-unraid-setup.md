# Keycloak Setup on Unraid Server

## Overview
Keycloak is an open-source Identity and Access Management solution that provides authentication, authorization, and user management. This guide covers setting up Keycloak on your Unraid server for your Unraid API project.

## Prerequisites
- Unraid server with Docker support (Community Applications plugin)
- PostgreSQL database (covered in separate guide)
- Custom Docker network (covered in network guide)
- Domain name (rezaunraid.net) with Cloudflare tunnel configured

## Keycloak Container Setup

### 1. Create Keycloak Directories on Unraid
```bash
# SSH into Unraid server
ssh root@your-unraid-ip

# Create directories for Keycloak data
mkdir -p /mnt/user/appdata/unraid-api/keycloak
mkdir -p /mnt/user/appdata/unraid-api/postgres
mkdir -p /mnt/user/appdata/unraid-api/redis
```

### 2. Docker Compose Configuration

#### docker-compose.yml for Keycloak
```yaml
version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: keycloak-postgres
    environment:
      POSTGRES_DB: keycloak
      POSTGRES_USER: keycloak
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - /mnt/user/appdata/unraid-api/postgres:/var/lib/postgresql/data
    networks:
      - unraid-api-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U keycloak"]
      interval: 30s
      timeout: 10s
      retries: 3

  keycloak:
    image: quay.io/keycloak/keycloak:23.0
    container_name: keycloak
    environment:
      # Database configuration
      KC_DB: postgres
      KC_DB_URL: ****************************************
      KC_DB_USERNAME: keycloak
      KC_DB_PASSWORD: ${POSTGRES_PASSWORD}
      
      # Keycloak admin user
      KEYCLOAK_ADMIN: admin
      KEYCLOAK_ADMIN_PASSWORD: ${KEYCLOAK_ADMIN_PASSWORD}
      
      # Hostname configuration
      KC_HOSTNAME: auth.rezaunraid.net
      KC_HOSTNAME_STRICT: false
      KC_HOSTNAME_STRICT_HTTPS: false
      
      # Proxy configuration for reverse proxy
      KC_PROXY: edge
      KC_HTTP_ENABLED: true
      
      # Health and metrics
      KC_HEALTH_ENABLED: true
      KC_METRICS_ENABLED: true
    command: start --optimized
    ports:
      - "8080:8080"
    volumes:
      - /mnt/user/appdata/unraid-api/keycloak:/opt/keycloak/data
    networks:
      - unraid-api-network
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8080/health/ready || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  unraid-api-network:
    external: true
```

### 3. Environment Variables

#### .env file
```env
# PostgreSQL Configuration
POSTGRES_PASSWORD=your_secure_postgres_password

# Keycloak Configuration
KEYCLOAK_ADMIN_PASSWORD=your_secure_admin_password

# Optional: Custom theme and configuration
KC_LOG_LEVEL=INFO
```

### 4. Deploy Keycloak on Unraid
```bash
# Navigate to your project directory on Unraid
cd /mnt/user/appdata/unraid-api

# Create the environment file
nano .env
# Add the environment variables above

# Start the services
docker-compose up -d

# Check logs
docker-compose logs -f keycloak

# Or check via Unraid Docker tab in web interface
```

## Keycloak Configuration

### 1. Initial Admin Setup
1. Access Keycloak at `http://your-unraid-ip:8080` or via Cloudflare tunnel at `https://auth.rezaunraid.net`
2. Login with admin credentials from environment variables
3. Create a new realm for your Unraid project

### 2. Create Realm
```bash
# Realm Name: unraid-realm
# Display Name: Unraid Control Panel
# Enabled: Yes
```

### 3. Configure Realm Settings
```json
{
  "realm": "unraid-realm",
  "displayName": "Unraid Control Panel",
  "enabled": true,
  "sslRequired": "external",
  "registrationAllowed": false,
  "loginWithEmailAllowed": true,
  "duplicateEmailsAllowed": false,
  "resetPasswordAllowed": true,
  "editUsernameAllowed": false,
  "bruteForceProtected": true,
  "permanentLockout": false,
  "maxFailureWaitSeconds": 900,
  "minimumQuickLoginWaitSeconds": 60,
  "waitIncrementSeconds": 60,
  "quickLoginCheckMilliSeconds": 1000,
  "maxDeltaTimeSeconds": 43200,
  "failureFactor": 30
}
```

### 4. Create Client for Your Application

#### Client Configuration
```json
{
  "clientId": "unraid-client",
  "name": "Unraid Control Panel Client",
  "description": "Client for Unraid Control Panel application",
  "enabled": true,
  "clientAuthenticatorType": "client-secret",
  "secret": "your-client-secret",
  "redirectUris": [
    "http://localhost:3000/*",
    "http://localhost:5173/*",
    "https://unraid.rezaunraid.net/*"
  ],
  "webOrigins": [
    "http://localhost:3000",
    "http://localhost:5173",
    "https://unraid.rezaunraid.net"
  ],
  "protocol": "openid-connect",
  "publicClient": false,
  "bearerOnly": false,
  "standardFlowEnabled": true,
  "implicitFlowEnabled": false,
  "directAccessGrantsEnabled": true,
  "serviceAccountsEnabled": true,
  "authorizationServicesEnabled": false
}
```

### 5. Create Roles
```bash
# Realm Roles
- admin: Full access to all Unraid functions
- user: Limited access to view-only functions
- operator: Can start/stop containers but not modify settings
```

### 6. Create Users
```json
{
  "username": "reza",
  "email": "<EMAIL>",
  "firstName": "Reza",
  "lastName": "Admin",
  "enabled": true,
  "emailVerified": true,
  "credentials": [
    {
      "type": "password",
      "value": "your_secure_password",
      "temporary": false
    }
  ],
  "realmRoles": ["admin"],
  "clientRoles": {
    "unraid-client": ["admin"]
  }
}
```

## Integration with Hono Backend

### 1. Install Dependencies
```bash
# In your server package
bun add jose @types/jsonwebtoken
```

### 2. JWT Verification Middleware
```typescript
// packages/server/src/middleware/auth.ts
import { Context, Next } from 'hono'
import { jwtVerify, createRemoteJWKSet } from 'jose'

const KEYCLOAK_URL = process.env.KEYCLOAK_URL || 'http://localhost:8080'
const REALM = process.env.KEYCLOAK_REALM || 'unraid-realm'

const JWKS = createRemoteJWKSet(
  new URL(`${KEYCLOAK_URL}/realms/${REALM}/protocol/openid-connect/certs`)
)

export async function authMiddleware(c: Context, next: Next) {
  const authHeader = c.req.header('Authorization')
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return c.json({ error: 'Missing or invalid authorization header' }, 401)
  }
  
  const token = authHeader.substring(7)
  
  try {
    const { payload } = await jwtVerify(token, JWKS, {
      issuer: `${KEYCLOAK_URL}/realms/${REALM}`,
      audience: 'unraid-client'
    })
    
    // Add user info to context
    c.set('user', payload)
    await next()
  } catch (error) {
    console.error('JWT verification failed:', error)
    return c.json({ error: 'Invalid token' }, 401)
  }
}

export function requireRole(role: string) {
  return async (c: Context, next: Next) => {
    const user = c.get('user')
    const realmAccess = user?.realm_access as { roles?: string[] }
    
    if (!realmAccess?.roles?.includes(role)) {
      return c.json({ error: 'Insufficient permissions' }, 403)
    }
    
    await next()
  }
}
```

### 3. Protected Routes Example
```typescript
// packages/server/src/routes/docker.ts
import { Hono } from 'hono'
import { authMiddleware, requireRole } from '../middleware/auth'

const dockerRoutes = new Hono()

// Apply auth middleware to all routes
dockerRoutes.use('*', authMiddleware)

// View containers (requires user role)
dockerRoutes.get('/', requireRole('user'), async (c) => {
  // Your container listing logic
})

// Start/stop containers (requires operator role)
dockerRoutes.post('/:name/start', requireRole('operator'), async (c) => {
  // Your container start logic
})

dockerRoutes.post('/:name/stop', requireRole('operator'), async (c) => {
  // Your container stop logic
})

// Admin operations (requires admin role)
dockerRoutes.delete('/:name', requireRole('admin'), async (c) => {
  // Your container deletion logic
})
```

## Nginx Reverse Proxy Configuration

### nginx.conf for Keycloak
```nginx
server {
    listen 80;
    server_name auth.rezaunraid.net;
    
    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
```

## Monitoring and Maintenance

### Health Checks
```bash
# Check Keycloak health
curl http://localhost:8080/health

# Check database connection
docker exec keycloak-postgres pg_isready -U keycloak

# View logs
docker-compose logs -f keycloak
```

### Backup Configuration
```bash
# Backup Keycloak realm configuration
docker exec keycloak /opt/keycloak/bin/kc.sh export --realm unraid-realm --file /tmp/realm-backup.json

# Copy backup from container
docker cp keycloak:/tmp/realm-backup.json ./backups/
```

### Updates
```bash
# Update Keycloak
docker-compose pull keycloak
docker-compose up -d keycloak
```

## Security Best Practices

1. **Use strong passwords** for admin and database accounts
2. **Enable HTTPS** in production with SSL certificates
3. **Configure rate limiting** to prevent brute force attacks
4. **Regular backups** of realm configuration and database
5. **Monitor logs** for suspicious activity
6. **Keep Keycloak updated** to latest security patches
7. **Use environment variables** for sensitive configuration
8. **Implement proper CORS** settings for your frontend

## Troubleshooting

### Common Issues
1. **Database connection errors**: Check PostgreSQL container health
2. **Authentication failures**: Verify client configuration and secrets
3. **CORS errors**: Check allowed origins in client settings
4. **SSL/TLS issues**: Verify proxy configuration and certificates

### Useful Commands
```bash
# Reset admin password
docker exec keycloak /opt/keycloak/bin/kc.sh reset-password --username admin

# View realm configuration
docker exec keycloak /opt/keycloak/bin/kc.sh show-config --realm unraid-realm
```