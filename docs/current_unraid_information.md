### Unraid Network Configuration
```bash
./unraid_net_info.sh
--- Unraid Network Configuration ---

Hostname: RezaUNRAID

DNS Servers: *************** 

Default Gateway: ***********

--- Network Interfaces ---
Interface       IPv4 Address       Netmask            MAC Address        MTU        Status          Type           
-----------------------------------------------------------------------------------------------------------------------
bond0           N/A (Slave of br0) N/A (Slave of br0) d8:43:ae:4b:c6:bb  1500       UP (Running)    Bond           
br0             ***********50      *************      d8:43:ae:4b:c6:bb  1500       UP (Running)    Other          
docker0         **********         ***********        02:42:59:04:ca:0f  1500       UP (Running)    Docker Bridge  
eth0            N/A                N/A                d8:43:ae:4b:c6:bb  1500       UP (Running)    Ethernet       
tailscale1      ************       ***************    N/A                1280       UP (Running)    Other          
tunl0           N/A                N/A                N/A                1480       DOWN            Tunnel/TAP     
veth2548949     N/A                N/A                8e:92:68:73:29:94  1500       UP (Running)    Virtual Eth    
veth8556383     N/A                N/A                6e:ab:2d:bf:e2:df  1500       UP (Running)    Virtual Eth    
veth01673cf     N/A                N/A                46:90:5a:61:7b:25  1500       UP (Running)    Virtual Eth    
veth1db3a82     N/A                N/A                c6:ce:8b:c3:df:33  1500       UP (Running)    Virtual Eth    
veth2a4e681     N/A                N/A                7e:8d:b7:69:59:17  1500       UP (Running)    Virtual Eth    
veth40d6d07     N/A                N/A                4a:62:3a:26:c7:6e  1500       UP (Running)    Virtual Eth    
veth4bae6ce     N/A                N/A                06:5d:fa:8f:d9:8d  1500       UP (Running)    Virtual Eth    
veth65beada     N/A                N/A                ce:26:2a:26:89:08  1500       UP (Running)    Virtual Eth    
veth690a18c     N/A                N/A                1a:d7:f1:0a:2d:7f  1500       UP (Running)    Virtual Eth    
veth7d08658     N/A                N/A                72:35:8b:d8:6a:ac  1500       UP (Running)    Virtual Eth    
veth7f6ef1f     N/A                N/A                76:c6:c8:56:ef:2e  1500       UP (Running)    Virtual Eth    
veth926f5b8     N/A                N/A                16:db:9e:a5:ee:a1  1500       UP (Running)    Virtual Eth    
veth9d77f46     N/A                N/A                de:91:b2:f0:a9:10  1500       UP (Running)    Virtual Eth    
vethbcc1223     N/A                N/A                e6:1e:c9:dd:eb:5d  1500       UP (Running)    Virtual Eth    
vethc0df598     N/A                N/A                16:73:3e:0a:2c:58  1500       UP (Running)    Virtual Eth    
vethd73f2a7     N/A                N/A                de:53:80:35:b3:64  1500       UP (Running)    Virtual Eth    
vethe0f80b0     N/A                N/A                de:24:aa:08:d4:1f  1500       UP (Running)    Virtual Eth    
vethe7dcbef     N/A                N/A                fe:a6:85:a7:fd:27  1500       UP (Running)    Virtual Eth    

--- Routing Table ---
Kernel IP routing table
Destination     Gateway         Genmask         Flags Metric Ref    Use Iface
0.0.0.0         ***********     0.0.0.0         UG    0      0        0 br0
**********      0.0.0.0         ***********     U     0      0        0 docker0
***********     0.0.0.0         *************   U     1      0        0 br0
*************   0.0.0.0         *************   U     0      0        0 br-ff4786919de3

--- Open Listening Ports (IPv4 TCP/UDP) ---
Proto   Local Addr Foreign Addr              State                     PID/Program name
---------------------------------------------------------------------------------
tcp     0.0.0.0:7878 0.0.0.0:*                 LISTEN                    866867/docker-proxy
tcp     0.0.0.0:7818 0.0.0.0:*                 LISTEN                    19237/docker-proxy
tcp     0.0.0.0:8191 0.0.0.0:*                 LISTEN                    18387/docker-proxy
tcp     0.0.0.0:8181 0.0.0.0:*                 LISTEN                    25751/docker-proxy
tcp     0.0.0.0:8086 0.0.0.0:*                 LISTEN                    995784/docker-proxy
tcp     0.0.0.0:8081 0.0.0.0:*                 LISTEN                    861982/docker-proxy
tcp     0.0.0.0:8080 0.0.0.0:*                 LISTEN                    24309/docker-proxy
tcp     0.0.0.0:8118 0.0.0.0:*                 LISTEN                    24325/docker-proxy
tcp     0.0.0.0:6379 0.0.0.0:*                 LISTEN                    24804/docker-proxy
tcp     0.0.0.0:6767 0.0.0.0:*                 LISTEN                    843470/docker-proxy
tcp     0.0.0.0:6881 0.0.0.0:*                 LISTEN                    24275/docker-proxy
tcp     0.0.0.0:5355 0.0.0.0:*                 LISTEN                    15701/wsdd2
tcp     0.0.0.0:5355 0.0.0.0:*                 LISTEN                    15701/wsdd2
tcp     0.0.0.0:5355 0.0.0.0:*                 LISTEN                    15701/wsdd2
tcp     0.0.0.0:5540 0.0.0.0:*                 LISTEN                    25066/docker-proxy
tcp     ************:445 0.0.0.0:*                 LISTEN                    15687/smbd
tcp     ************:80 0.0.0.0:*                 LISTEN                    11309/nginx:
tcp     ************:22 0.0.0.0:*                 LISTEN                    15580/sshd:
tcp     0.0.0.0:6080 0.0.0.0:*                 LISTEN                    18556/docker-proxy
tcp     ************:139 0.0.0.0:*                 LISTEN                    15687/smbd
tcp     0.0.0.0:5055 0.0.0.0:*                 LISTEN                    19625/docker-proxy
tcp     0.0.0.0:3493 0.0.0.0:*                 LISTEN                    5500/upsd
tcp     0.0.0.0:3702 0.0.0.0:*                 LISTEN                    15701/wsdd2
tcp     0.0.0.0:3702 0.0.0.0:*                 LISTEN                    15701/wsdd2
tcp     0.0.0.0:3702 0.0.0.0:*                 LISTEN                    15701/wsdd2
tcp     127.0.0.1:35059 0.0.0.0:*                 LISTEN                    291604/Plex
tcp     0.0.0.0:18443 0.0.0.0:*                 LISTEN                    19207/docker-proxy
tcp     0.0.0.0:1880 0.0.0.0:*                 LISTEN                    19223/docker-proxy
tcp     127.0.0.1:139 0.0.0.0:*                 LISTEN                    15687/smbd
tcp     127.0.0.1:80 0.0.0.0:*                 LISTEN                    11309/nginx:
tcp     127.0.0.1:445 0.0.0.0:*                 LISTEN                    15687/smbd
tcp     127.0.0.1:443 0.0.0.0:*                 LISTEN                    11309/nginx:
tcp     ************:5252 0.0.0.0:*                 LISTEN                    14236/tailscaled
tcp     127.0.0.1:32401 0.0.0.0:*                 LISTEN                    290922/Plex
tcp     127.0.0.1:32600 0.0.0.0:*                 LISTEN                    291737/Plex
tcp     ************:61074 0.0.0.0:*                 LISTEN                    14236/tailscaled
tcp     ***********50:80 0.0.0.0:*                 LISTEN                    11309/nginx:
tcp     ***********50:22 0.0.0.0:*                 LISTEN                    15580/sshd:
tcp     ***********50:139 0.0.0.0:*                 LISTEN                    15687/smbd
tcp     0.0.0.0:46495 0.0.0.0:*                 LISTEN                    18072/docker-proxy
tcp     ***********50:445 0.0.0.0:*                 LISTEN                    15687/smbd
tcp     0.0.0.0:27017 0.0.0.0:*                 LISTEN                    19012/docker-proxy
tcp     0.0.0.0:9696 0.0.0.0:*                 LISTEN                    865006/docker-proxy
tcp     0.0.0.0:9897 0.0.0.0:*                 LISTEN                    870565/docker-proxy
tcp     0.0.0.0:8989 0.0.0.0:*                 LISTEN                    870550/docker-proxy
tcp6    :::7878    :::*                      LISTEN                    866877/docker-proxy
tcp6    :::7818    :::*                      LISTEN                    19244/docker-proxy
tcp6    :::8191    :::*                      LISTEN                    18394/docker-proxy
tcp6    :::8181    :::*                      LISTEN                    25761/docker-proxy
tcp6    :::8086    :::*                      LISTEN                    995792/docker-proxy
tcp6    :::8081    :::*                      LISTEN                    861989/docker-proxy
tcp6    :::8080    :::*                      LISTEN                    24317/docker-proxy
tcp6    :::8118    :::*                      LISTEN                    24333/docker-proxy
tcp6    :::6379    :::*                      LISTEN                    24811/docker-proxy
tcp6    :::6767    :::*                      LISTEN                    843477/docker-proxy
tcp6    :::6881    :::*                      LISTEN                    24282/docker-proxy
tcp6    :::7090    :::*                      LISTEN                    17160/unbalanced
tcp6    :::5355    :::*                      LISTEN                    15701/wsdd2
tcp6    :::5355    :::*                      LISTEN                    15701/wsdd2
tcp6    :::5540    :::*                      LISTEN                    25074/docker-proxy
tcp6    :::6080    :::*                      LISTEN                    18563/docker-proxy
tcp6    fd7a:115c:a1e0::66:5252 :::*                      LISTEN                    14236/tailscaled
tcp6    :::5055    :::*                      LISTEN                    19633/docker-proxy
tcp6    :::3702    :::*                      LISTEN                    15701/wsdd2
tcp6    :::3702    :::*                      LISTEN                    15701/wsdd2
tcp6    :::18443   :::*                      LISTEN                    19214/docker-proxy
tcp6    fd7a:115c:a1e0::660:445 :::*                      LISTEN                    15687/smbd
tcp6    fd7a:115c:a1e0::6601:80 :::*                      LISTEN                    11309/nginx:
tcp6    fd7a:115c:a1e0::6601:22 :::*                      LISTEN                    15580/sshd:
tcp6    fd7a:115c:a1e0::660:139 :::*                      LISTEN                    15687/smbd
tcp6    ::1:80     :::*                      LISTEN                    11309/nginx:
tcp6    :::1880    :::*                      LISTEN                    19229/docker-proxy
tcp6    ::1:443    :::*                      LISTEN                    11309/nginx:
tcp6    :::32400   :::*                      LISTEN                    290922/Plex
tcp6    :::46495   :::*                      LISTEN                    18080/docker-proxy
tcp6    fd7a:115c:a1e0::6:60714 :::*                      LISTEN                    14236/tailscaled
tcp6    :::27017   :::*                      LISTEN                    19019/docker-proxy
tcp6    :::9696    :::*                      LISTEN                    865012/docker-proxy
tcp6    :::9897    :::*                      LISTEN                    870574/docker-proxy
tcp6    :::8989    :::*                      LISTEN                    870557/docker-proxy

--- Docker Information ---
Docker Status: Running

--- Docker Networks ---
NETWORK ID     NAME          DRIVER    SCOPE
be8bf07bc798   br0           ipvlan    local
307f8cd4db2c   bridge        bridge    local
d21daf890237   host          host      local
ff00fffa6b49   none          null      local
ff4786919de3   reza-unraid   bridge    local

--- Running Docker Containers ---
CONTAINER ID                                                       NAMES                   IMAGE                               PORTS                                                                                                                                                                        STATUS                 SIZE
038da6d2d29f74116571527ea745cada3c86ed13bee145012a350441322e94ee   Sonarr                  binhex/arch-sonarr                  0.0.0.0:8989->8989/tcp, :::8989->8989/tcp, 0.0.0.0:9897->9897/tcp, :::9897->9897/tcp                                                                                         Up 3 days (healthy)    876MB (virtual 2.11GB)
836b69870f13d621e03d9e160dd938851bf78a1ac788f31c1f6caaa6c0b42a07   Radarr                  binhex/arch-radarr                  0.0.0.0:7878->7878/tcp, :::7878->7878/tcp                                                                                                                                    Up 3 days (healthy)    168MB (virtual 1.4GB)
0ca3c5fc8670e5978d80212902baa7a7f99a4c3a1079bc4f42026992397e49e5   Prowlarr                binhex/arch-prowlarr                0.0.0.0:9696->9696/tcp, :::9696->9696/tcp                                                                                                                                    Up 3 days (healthy)    444MB (virtual 2.04GB)
d9c4019feeb4f17a900753b505c395516aa59cd89aea2ae40ef37a9f87e125c7   MeTube                  alexta69/metube                     0.0.0.0:8081->8081/tcp, :::8081->8081/tcp                                                                                                                                    Up 3 days              1.27MB (virtual 325MB)
fc18a47ce14fd4bfd25670f699ea95b82b9723c372380245d1795525bc8015ea   Bazarr                  lscr.io/linuxserver/bazarr:latest   0.0.0.0:6767->6767/tcp, :::6767->6767/tcp                                                                                                                                    Up 3 days              22.7kB (virtual 422MB)
e013a02e1539e0c401de694c47cd25052051f2b029ea99c0c4231ce3ad3acfe2   FileBrowser-PNP         filebrowser/filebrowser             0.0.0.0:8086->80/tcp, :::8086->80/tcp                                                                                                                                        Up 5 days (healthy)    0B (virtual 22.7MB)
ddefdf8f87e69c7537fd44ad1073ae0a5b3bd7d93bcdf9c6401f0f4214c13597   Redis                   redis                               0.0.0.0:6379->6379/tcp, :::6379->6379/tcp                                                                                                                                    Up 5 days              21.6MB (virtual 149MB)
bfd341adb40159b45da3f988de11af590ce3bd036b8e39957010d4e2079b7f5c   MongoDB                 mongo                               0.0.0.0:27017->27017/tcp, :::27017->27017/tcp                                                                                                                                Up 5 days              0B (virtual 908MB)
4494792686ef1f356f148b8e03d087d6893c3c76ac629b8dea7060354f820f3b   qBittorrent             binhex/arch-qbittorrentvpn          0.0.0.0:6881->6881/tcp, :::6881->6881/tcp, 0.0.0.0:8080->8080/tcp, :::8080->8080/tcp, 0.0.0.0:8118->8118/tcp, 0.0.0.0:6881->6881/udp, :::8118->8118/tcp, :::6881->6881/udp   Up 5 days              6.22MB (virtual 1.79GB)
29c4203648252a6430ea4ba1beb65308ea738d7f1b18df521ba48c064d82fc6d   Azure-Pipelines-Agent   chrizzo84/azpipeline                                                                                                                                                                                             Up 5 days              1.4GB (virtual 3.97GB)
eecc35a1ff6c4b67e74161d54cc9933ed085a79121fbc233ba93e4aae1ed01ba   Plex                    plexinc/pms-docker                                                                                                                                                                                               Up 8 hours (healthy)   355kB (virtual 355MB)
b06a4101c7e627bcd036f6eba94d071bea85e93b8cf310a433a62e99391cc3c7   RedisInsight            redis/redisinsight:latest           0.0.0.0:5540->5540/tcp, :::5540->5540/tcp                                                                                                                                    Up 5 days              0B (virtual 306MB)
e4afb2de1fbf1cab954272e6130808afb5e677a1039c3e0df5419cb08ff3509d   NginxProxyManager       jlesage/nginx-proxy-manager         0.0.0.0:18443->4443/tcp, :::18443->4443/tcp, 0.0.0.0:1880->8080/tcp, :::1880->8080/tcp, 0.0.0.0:7818->8181/tcp, :::7818->8181/tcp                                            Up 5 days              13kB (virtual 191MB)
ba840f8aad38b74deb5def3454cea29cc7a125ce4a385a02474cb21ecfdfcbef   Flaresolverr            binhex/arch-flaresolverr            0.0.0.0:8191->8191/tcp, :::8191->8191/tcp                                                                                                                                    Up 5 days              131MB (virtual 1.65GB)
62dd87a3ac99fb3002bcd0ddd9fcf925e8192700198e2bee7ce70a8a6ccd6031   tautulli                tautulli/tautulli:latest            0.0.0.0:8181->8181/tcp, :::8181->8181/tcp                                                                                                                                    Up 5 days (healthy)    2.66kB (virtual 204MB)
b02d0177945861b7473dc4fdabc9edde9c3e42548a5a15dcb54c4a5da04977bb   Overseerr               binhex/arch-overseerr               0.0.0.0:5055->5055/tcp, :::5055->5055/tcp                                                                                                                                    Up 5 days              1.19GB (virtual 3.49GB)
f6ba77f20ffc732351671c7f5e9a8212aadcf5b2cda6b07c87eda3aca737faef   Krusader                binhex/arch-krusader                0.0.0.0:6080->6080/tcp, :::6080->6080/tcp                                                                                                                                    Up 5 days              28.6MB (virtual 3.3GB)
bb01ef708cf0655dda59b27322fbe3c64fcfc96fce5dc494a4e376890e9d58b0   Cloudflared-Tunnel      figro/unraid-cloudflared-tunnel     0.0.0.0:46495->46495/tcp, :::46495->46495/tcp                                                                                                                                Up 5 days              41.2MB (virtual 50.1MB)
da0bc86e261b251865d62cc109b320a45ae21933748ad95dddbe3553c6183478   Cloudflare-DDNS         oznu/cloudflare-ddns                                                                                                                                                                                             Up 5 days              5.81kB (virtual 36.3MB)

--- Docker Container IP Addresses ---
Container Name  Network                   IP Address                MAC Address         
---------------------------------------------------------------------------------------
Sonarr          reza-unraid               *************5            02:42:c0:a8:64:0f   
Radarr          reza-unraid               *************2            02:42:c0:a8:64:0c   
Prowlarr        reza-unraid               *************1            02:42:c0:a8:64:0b   
MeTube          reza-unraid               *************             02:42:c0:a8:64:07   
Bazarr          reza-unraid               *************             02:42:c0:a8:64:03   
FileBrowser-PNP reza-unraid               *************6            02:42:c0:a8:64:10   
Redis           reza-unraid               *************3            02:42:c0:a8:64:0d   
MongoDB         reza-unraid               *************             02:42:c0:a8:64:08   
qBittorrent     bridge                    **********                02:42:ac:11:00:03   
Azure-Pipelines-Agent reza-unraid               *************             02:42:c0:a8:64:02   
Plex            host                                                N/A                 
RedisInsight    reza-unraid               *************4            02:42:c0:a8:64:0e   
NginxProxyManager reza-unraid               *************             02:42:c0:a8:64:09   
Flaresolverr    reza-unraid               *************             02:42:c0:a8:64:05   
tautulli        bridge                    **********                02:42:ac:11:00:04   
Overseerr       reza-unraid               **************            02:42:c0:a8:64:0a   
Krusader        reza-unraid               *************             02:42:c0:a8:64:06   
Cloudflared-Tunnel reza-unraid               *************             02:42:c0:a8:64:04   
Cloudflare-DDNS bridge                    **********                02:42:ac:11:00:02   
```

### Inspecting reza-unraid network
```bash
docker network inspect reza-unraid
[
    {
        "Name": "reza-unraid",
        "Id": "ff4786919de393aa66f4067a6717d6f63f91e9fbb313d82b0dd817d5fee9c438",
        "Created": "2024-07-01T00:03:43.976899718+02:00",
        "Scope": "local",
        "Driver": "bridge",
        "EnableIPv6": false,
        "IPAM": {
            "Driver": "default",
            "Options": {},
            "Config": [
                {
                    "Subnet": "*************/24",
                    "Gateway": "*************"
                }
            ]
        },
        "Internal": false,
        "Attachable": false,
        "Ingress": false,
        "ConfigFrom": {
            "Network": ""
        },
        "ConfigOnly": false,
        "Containers": {
            "038da6d2d29f74116571527ea745cada3c86ed13bee145012a350441322e94ee": {
                "Name": "Sonarr",
                "EndpointID": "2759b2b2b8468153632ac16a44f856f4ce732f07841ec3a0449125ae1b4f193b",
                "MacAddress": "02:42:c0:a8:64:0c",
                "IPv4Address": "*************2/24",
                "IPv6Address": ""
            },
            "0ca3c5fc8670e5978d80212902baa7a7f99a4c3a1079bc4f42026992397e49e5": {
                "Name": "Prowlarr",
                "EndpointID": "c08c52fbaee8027a7ab131769d700438126aa1701da2401bf7e5529a8913985f",
                "MacAddress": "02:42:c0:a8:64:0b",
                "IPv4Address": "*************1/24",
                "IPv6Address": ""
            },
            "19facabca4d2e724d5cf9cf158afea628b00754f471275ef8a637a378d8ce6d4": {
                "Name": "Flaresolverr",
                "EndpointID": "431b39f1a8f8b19139284c70301acae33b0806053c5b6994fb79a8e6a4e7d95b",
                "MacAddress": "02:42:c0:a8:64:05",
                "IPv4Address": "*************/24",
                "IPv6Address": ""
            },
            "29c4203648252a6430ea4ba1beb65308ea738d7f1b18df521ba48c064d82fc6d": {
                "Name": "Azure-Pipelines-Agent",
                "EndpointID": "4ce83349060204a0d89a25e511c508ea91fbe4e0bbb897b1078cfabc4caf8bcd",
                "MacAddress": "02:42:c0:a8:64:02",
                "IPv4Address": "*************/24",
                "IPv6Address": ""
            },
            "401c289d8f2cf917001fd32a5ec0132e83c545f1a3d22f956dcb0d32822ec01c": {
                "Name": "Bazarr",
                "EndpointID": "86be9d5446702ad214383c1e88cff55a4024f64c68eeed17eb03cfe12e1eb3a1",
                "MacAddress": "02:42:c0:a8:64:07",
                "IPv4Address": "*************/24",
                "IPv6Address": ""
            },
            "7581eb7daa9d29c788ae7359a8328cf5ee686c41fbeaae6192b58e016cacea04": {
                "Name": "Cleanarr",
                "EndpointID": "dae47a23bdf0fe587014ec6039569478f2ee1968a8f6f452bc285e1a5dab1fc1",
                "MacAddress": "02:42:c0:a8:64:10",
                "IPv4Address": "*************6/24",
                "IPv6Address": ""
            },
            "836b69870f13d621e03d9e160dd938851bf78a1ac788f31c1f6caaa6c0b42a07": {
                "Name": "Radarr",
                "EndpointID": "687cb99b4ae49109898baddafcd80042e537be564c789d5fe642b0dbfb656140",
                "MacAddress": "02:42:c0:a8:64:03",
                "IPv4Address": "*************/24",
                "IPv6Address": ""
            },
            "a49543a0a8b864205a4e22c382fc106b4658c1483f22d4aa51fb0e5ffe11bb58": {
                "Name": "Redis",
                "EndpointID": "aec9f7961b1ac5076f93d9d562ed115c968d4aabf59a01168fbabfa7e6b0b82f",
                "MacAddress": "02:42:c0:a8:64:0d",
                "IPv4Address": "*************3/24",
                "IPv6Address": ""
            },
            "b02d0177945861b7473dc4fdabc9edde9c3e42548a5a15dcb54c4a5da04977bb": {
                "Name": "Overseerr",
                "EndpointID": "187e667cca1c0444dc22a46950adec163197cad17ea0b29d0d4227d10ba6a605",
                "MacAddress": "02:42:c0:a8:64:0a",
                "IPv4Address": "**************/24",
                "IPv6Address": ""
            },
            "b06a4101c7e627bcd036f6eba94d071bea85e93b8cf310a433a62e99391cc3c7": {
                "Name": "RedisInsight",
                "EndpointID": "86bdb40915e0657d1ee19d44d4d942b6e9ffd969db57ca0ff401b660148aefbf",
                "MacAddress": "02:42:c0:a8:64:0e",
                "IPv4Address": "*************4/24",
                "IPv6Address": ""
            },
            "bb01ef708cf0655dda59b27322fbe3c64fcfc96fce5dc494a4e376890e9d58b0": {
                "Name": "Cloudflared-Tunnel",
                "EndpointID": "56892af6ea63e87bd1d505cefa4f5db9ab2075da4d701e72b6fc325acb825e01",
                "MacAddress": "02:42:c0:a8:64:04",
                "IPv4Address": "*************/24",
                "IPv6Address": ""
            },
            "bfd341adb40159b45da3f988de11af590ce3bd036b8e39957010d4e2079b7f5c": {
                "Name": "MongoDB",
                "EndpointID": "b5d7dd1a9bf967db67c9777486c56e9537c285504e34968aba31cab5513d334b",
                "MacAddress": "02:42:c0:a8:64:08",
                "IPv4Address": "*************/24",
                "IPv6Address": ""
            },
            "e4afb2de1fbf1cab954272e6130808afb5e677a1039c3e0df5419cb08ff3509d": {
                "Name": "NginxProxyManager",
                "EndpointID": "699f204d63f613285647a58d991a372010bf74b2abbf3fe19481d29cb9fd5d59",
                "MacAddress": "02:42:c0:a8:64:09",
                "IPv4Address": "*************/24",
                "IPv6Address": ""
            },
            "f6ba77f20ffc732351671c7f5e9a8212aadcf5b2cda6b07c87eda3aca737faef": {
                "Name": "Krusader",
                "EndpointID": "13f0739e8ebe47c01f1e6cd730f8db965ee62abbce8e3dfde8dc8dfd8bd663c1",
                "MacAddress": "02:42:c0:a8:64:06",
                "IPv4Address": "*************/24",
                "IPv6Address": ""
            }
        },
        "Options": {},
        "Labels": {}
    }
]
```