# Docker Network Setup on TrueNAS

## Overview
This guide covers creating and managing custom Docker networks on TrueNAS for your Keycloak, PostgreSQL, and other containerized services. Custom networks provide better isolation, security, and communication between containers.

## Understanding Docker Networks

### Network Types
1. **Bridge**: Default network type, containers can communicate with each other
2. **Host**: Container uses host's network stack directly
3. **None**: Container has no network access
4. **Custom Bridge**: User-defined bridge networks with enhanced features

### Benefits of Custom Networks
- **Service Discovery**: Containers can communicate using service names
- **Isolation**: Better security isolation from default bridge
- **Custom DNS**: Built-in DNS resolution between containers
- **IP Address Management**: Control over IP address assignment
- **Network Policies**: Fine-grained control over container communication

## Creating Custom Docker Network

### 1. SSH into TrueNAS
```bash
ssh admin@your-truenas-ip
```

### 2. Create Custom Bridge Network
```bash
# Create a custom bridge network for your Unraid project
docker network create \
  --driver bridge \
  --subnet=**********/16 \
  --ip-range=************/20 \
  --gateway=********** \
  unraid-network

# Verify network creation
docker network ls

# Inspect network details
docker network inspect unraid-network
```

### 3. Advanced Network Configuration
```bash
# Create network with custom DNS and labels
docker network create \
  --driver bridge \
  --subnet=**********/16 \
  --ip-range=************/20 \
  --gateway=********** \
  --dns=******* \
  --dns=******* \
  --label project=unraid-api \
  --label environment=production \
  unraid-network
```

## Network Configuration for Services

### 1. Docker Compose with Custom Network
```yaml
# docker-compose.yml
version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: keycloak-postgres
    environment:
      POSTGRES_DB: keycloak
      POSTGRES_USER: keycloak
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      unraid-network:
        ipv4_address: **********0
    restart: unless-stopped

  keycloak:
    image: quay.io/keycloak/keycloak:23.0
    container_name: keycloak
    environment:
      KC_DB: postgres
      KC_DB_URL: ****************************************
      KC_DB_USERNAME: keycloak
      KC_DB_PASSWORD: ${POSTGRES_PASSWORD}
      KEYCLOAK_ADMIN: admin
      KEYCLOAK_ADMIN_PASSWORD: ${KEYCLOAK_ADMIN_PASSWORD}
    ports:
      - "8080:8080"
    networks:
      unraid-network:
        ipv4_address: **********1
    depends_on:
      - postgres
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: redis-cache
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    networks:
      unraid-network:
        ipv4_address: **********2
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    container_name: nginx-proxy
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    networks:
      unraid-network:
        ipv4_address: ***********
    depends_on:
      - keycloak
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  unraid-network:
    external: true
```

### 2. Individual Container Network Assignment
```bash
# Run container with specific network and IP
docker run -d \
  --name my-app \
  --network unraid-network \
  --ip *********** \
  my-app:latest

# Connect existing container to network
docker network connect unraid-network existing-container

# Disconnect container from network
docker network disconnect unraid-network container-name
```

## Network Security and Isolation

### 1. Network Policies with iptables
```bash
# Allow communication within the custom network
iptables -A DOCKER-USER -s **********/16 -d **********/16 -j ACCEPT

# Block access from custom network to host
iptables -A DOCKER-USER -s **********/16 -d ***********/24 -j DROP

# Allow specific ports from custom network to host
iptables -A DOCKER-USER -s **********/16 -d ************* -p tcp --dport 22 -j ACCEPT

# Save iptables rules (TrueNAS specific)
service netif restart
```

### 2. Container Communication Rules
```yaml
# docker-compose.yml with network aliases
services:
  database:
    image: postgres:15
    networks:
      unraid-network:
        aliases:
          - db
          - postgres-server

  app:
    image: my-app:latest
    networks:
      unraid-network:
        aliases:
          - api-server
    environment:
      DATABASE_URL: ******************************/mydb
```

## Monitoring and Troubleshooting

### 1. Network Inspection Commands
```bash
# List all networks
docker network ls

# Inspect specific network
docker network inspect unraid-network

# Show containers in network
docker network inspect unraid-network --format='{{range .Containers}}{{.Name}} {{.IPv4Address}}{{"\n"}}{{end}}'

# Check container network settings
docker inspect container-name --format='{{.NetworkSettings.Networks}}'
```

### 2. Connectivity Testing
```bash
# Test connectivity between containers
docker exec container1 ping container2

# Test DNS resolution
docker exec container1 nslookup container2

# Check open ports
docker exec container1 netstat -tulpn

# Test specific port connectivity
docker exec container1 telnet container2 5432
```

### 3. Network Traffic Analysis
```bash
# Monitor network traffic
docker exec container1 tcpdump -i eth0

# Check network statistics
docker exec container1 cat /proc/net/dev

# Monitor real-time connections
docker exec container1 ss -tuln
```

## Advanced Network Configurations

### 1. Multiple Networks per Container
```yaml
services:
  app:
    image: my-app:latest
    networks:
      - frontend-network
      - backend-network

networks:
  frontend-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
  backend-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
```

### 2. Network with External Access Control
```yaml
services:
  web:
    image: nginx:alpine
    ports:
      - "80:80"
    networks:
      - public-network
      - internal-network

  api:
    image: my-api:latest
    networks:
      - internal-network
    # No ports exposed, only accessible via internal network

networks:
  public-network:
    driver: bridge
  internal-network:
    driver: bridge
    internal: true  # No external access
```

## Network Backup and Migration

### 1. Export Network Configuration
```bash
# Export network configuration
docker network inspect unraid-network > unraid-network-config.json

# Create network from backup
docker network create \
  --driver bridge \
  --subnet=$(cat unraid-network-config.json | jq -r '.[0].IPAM.Config[0].Subnet') \
  --gateway=$(cat unraid-network-config.json | jq -r '.[0].IPAM.Config[0].Gateway') \
  unraid-network-restored
```

### 2. Network Migration Script
```bash
#!/bin/bash
# migrate-network.sh

OLD_NETWORK="old-network"
NEW_NETWORK="new-network"

# Get all containers in old network
CONTAINERS=$(docker network inspect $OLD_NETWORK --format='{{range .Containers}}{{.Name}} {{end}}')

# Connect containers to new network
for container in $CONTAINERS; do
    echo "Migrating $container to $NEW_NETWORK"
    docker network connect $NEW_NETWORK $container
    docker network disconnect $OLD_NETWORK $container
done

echo "Migration complete"
```

## Performance Optimization

### 1. Network Driver Selection
```bash
# Use macvlan for better performance (requires host network access)
docker network create -d macvlan \
  --subnet=***********/24 \
  --gateway=*********** \
  -o parent=eth0 \
  macvlan-network

# Use overlay for multi-host setups
docker network create -d overlay \
  --subnet=10.0.0.0/24 \
  overlay-network
```

### 2. Network Performance Tuning
```bash
# Increase network buffer sizes
echo 'net.core.rmem_max = 134217728' >> /etc/sysctl.conf
echo 'net.core.wmem_max = 134217728' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_rmem = 4096 87380 134217728' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_wmem = 4096 65536 134217728' >> /etc/sysctl.conf

# Apply changes
sysctl -p
```

## Cleanup and Maintenance

### 1. Network Cleanup Commands
```bash
# Remove unused networks
docker network prune

# Remove specific network (must disconnect all containers first)
docker network rm unraid-network

# Force remove network
docker network rm -f unraid-network

# Clean up all unused Docker resources
docker system prune -a
```

### 2. Regular Maintenance Script
```bash
#!/bin/bash
# network-maintenance.sh

echo "Starting network maintenance..."

# Check network health
docker network ls --filter "dangling=true" -q | xargs -r docker network rm

# Log network statistics
docker network inspect unraid-network --format='{{.Name}}: {{len .Containers}} containers' >> /var/log/docker-networks.log

# Check for IP conflicts
docker network inspect unraid-network --format='{{range .Containers}}{{.IPv4Address}}{{"\n"}}{{end}}' | sort | uniq -d

echo "Network maintenance complete"
```

## Integration with Your Project

### Environment Variables for Network Configuration
```env
# .env
DOCKER_NETWORK=unraid-network
POSTGRES_IP=**********0
KEYCLOAK_IP=**********1
REDIS_IP=**********2
NGINX_IP=***********
```

### Application Configuration
```typescript
// packages/server/src/config/network.ts
export const networkConfig = {
  postgres: {
    host: process.env.POSTGRES_IP || 'postgres',
    port: 5432,
  },
  redis: {
    host: process.env.REDIS_IP || 'redis',
    port: 6379,
  },
  keycloak: {
    host: process.env.KEYCLOAK_IP || 'keycloak',
    port: 8080,
  }
}
```
