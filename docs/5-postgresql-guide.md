# PostgreSQL Guide for Hono Integration

## Overview
PostgreSQL is a powerful, open-source relational database system. This guide covers PostgreSQL fundamentals, useful SQL commands, and integration with your Hono backend for the Unraid API project.

## What is PostgreSQL?

PostgreSQL is an advanced, enterprise-class, and open-source relational database system. It supports both SQL (relational) and JSON (non-relational) querying.

### Key Features
- **ACID Compliance**: Ensures data integrity
- **Extensibility**: Custom data types, operators, and functions
- **Concurrency**: Multi-version concurrency control (MVCC)
- **Performance**: Advanced indexing and query optimization
- **JSON Support**: Native JSON and JSONB data types
- **Full-text Search**: Built-in text search capabilities

## PostgreSQL Setup with Docker

### 1. Docker Compose Configuration
```yaml
version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: unraid-postgres
    environment:
      POSTGRES_DB: unraid_db
      POSTGRES_USER: unraid_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - unraid-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U unraid_user -d unraid_db"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:

networks:
  unraid-network:
    external: true
```

### 2. Environment Configuration
```env
# .env
POSTGRES_PASSWORD=your_secure_password
DATABASE_URL=postgresql://unraid_user:your_secure_password@localhost:5432/unraid_db
```

## Database Schema Design

### 1. Initial Schema (init-scripts/01-schema.sql)
```sql
-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Users table (for application-specific user data)
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    keycloak_id VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true
);

-- Docker containers tracking
CREATE TABLE docker_containers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) UNIQUE NOT NULL,
    image VARCHAR(500) NOT NULL,
    state VARCHAR(50) NOT NULL,
    status VARCHAR(255),
    ports JSONB,
    environment JSONB,
    volumes JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- System metrics
CREATE TABLE system_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    metric_type VARCHAR(100) NOT NULL,
    metric_name VARCHAR(255) NOT NULL,
    value NUMERIC,
    unit VARCHAR(50),
    metadata JSONB,
    recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User sessions and activity logs
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_active BOOLEAN DEFAULT true
);

-- Audit logs
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    action VARCHAR(255) NOT NULL,
    resource_type VARCHAR(100),
    resource_id VARCHAR(255),
    details JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_users_keycloak_id ON users(keycloak_id);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_docker_containers_name ON docker_containers(name);
CREATE INDEX idx_docker_containers_state ON docker_containers(state);
CREATE INDEX idx_system_metrics_type_name ON system_metrics(metric_type, metric_name);
CREATE INDEX idx_system_metrics_recorded_at ON system_metrics(recorded_at);
CREATE INDEX idx_user_sessions_token ON user_sessions(session_token);
CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);
```

### 2. Triggers and Functions (init-scripts/02-functions.sql)
```sql
-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_docker_containers_updated_at 
    BEFORE UPDATE ON docker_containers 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to clean old metrics
CREATE OR REPLACE FUNCTION clean_old_metrics()
RETURNS void AS $$
BEGIN
    DELETE FROM system_metrics 
    WHERE recorded_at < NOW() - INTERVAL '30 days';
END;
$$ LANGUAGE plpgsql;

-- Function to clean expired sessions
CREATE OR REPLACE FUNCTION clean_expired_sessions()
RETURNS void AS $$
BEGIN
    DELETE FROM user_sessions 
    WHERE expires_at < NOW() OR is_active = false;
END;
$$ LANGUAGE plpgsql;
```

## Essential SQL Commands

### 1. Basic CRUD Operations
```sql
-- INSERT
INSERT INTO users (keycloak_id, username, email, first_name, last_name)
VALUES ('kc-123', 'reza', '<EMAIL>', 'Reza', 'Admin');

-- SELECT
SELECT * FROM users WHERE is_active = true;
SELECT username, email, created_at FROM users ORDER BY created_at DESC;

-- UPDATE
UPDATE users 
SET last_login = NOW() 
WHERE keycloak_id = 'kc-123';

-- DELETE
DELETE FROM users WHERE is_active = false AND created_at < NOW() - INTERVAL '1 year';
```

### 2. Advanced Queries
```sql
-- JSON operations
SELECT name, ports->>'80/tcp' as http_port 
FROM docker_containers 
WHERE ports ? '80/tcp';

-- Aggregations
SELECT 
    metric_type,
    AVG(value) as avg_value,
    MAX(value) as max_value,
    COUNT(*) as count
FROM system_metrics 
WHERE recorded_at > NOW() - INTERVAL '1 hour'
GROUP BY metric_type;

-- Window functions
SELECT 
    name,
    state,
    updated_at,
    LAG(state) OVER (PARTITION BY name ORDER BY updated_at) as previous_state
FROM docker_containers
ORDER BY name, updated_at;

-- Common Table Expressions (CTE)
WITH active_users AS (
    SELECT user_id, COUNT(*) as session_count
    FROM user_sessions 
    WHERE is_active = true
    GROUP BY user_id
)
SELECT u.username, u.email, au.session_count
FROM users u
JOIN active_users au ON u.id = au.user_id;
```

### 3. Performance and Maintenance
```sql
-- Analyze table statistics
ANALYZE users;

-- Vacuum to reclaim space
VACUUM ANALYZE docker_containers;

-- Check table sizes
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- Check index usage
SELECT 
    indexrelname,
    idx_tup_read,
    idx_tup_fetch,
    idx_scan
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;
```

## Integration with Hono

### 1. Database Connection Setup
```bash
# Install PostgreSQL client for Bun
bun add postgres
bun add -d @types/pg
```

### 2. Database Configuration
```typescript
// packages/server/src/config/database.ts
import { Pool } from 'postgres'

const connectionString = process.env.DATABASE_URL || 'postgresql://unraid_user:password@localhost:5432/unraid_db'

export const db = new Pool({
  connectionString,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
})

// Test connection
export async function testConnection() {
  try {
    const client = await db.connect()
    const result = await client.query('SELECT NOW()')
    client.release()
    console.log('✅ Database connected successfully:', result.rows[0])
    return true
  } catch (error) {
    console.error('❌ Database connection failed:', error)
    return false
  }
}
```

### 3. Database Service Layer
```typescript
// packages/server/src/services/database.ts
import { db } from '../config/database'

export class DatabaseService {
  // User operations
  async createUser(userData: {
    keycloakId: string
    username: string
    email: string
    firstName?: string
    lastName?: string
  }) {
    const query = `
      INSERT INTO users (keycloak_id, username, email, first_name, last_name)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `
    const values = [userData.keycloakId, userData.username, userData.email, userData.firstName, userData.lastName]
    const result = await db.query(query, values)
    return result.rows[0]
  }

  async getUserByKeycloakId(keycloakId: string) {
    const query = 'SELECT * FROM users WHERE keycloak_id = $1 AND is_active = true'
    const result = await db.query(query, [keycloakId])
    return result.rows[0]
  }

  async updateUserLastLogin(userId: string) {
    const query = 'UPDATE users SET last_login = NOW() WHERE id = $1'
    await db.query(query, [userId])
  }

  // Docker container operations
  async upsertContainer(containerData: {
    name: string
    image: string
    state: string
    status?: string
    ports?: any
    environment?: any
    volumes?: any
  }) {
    const query = `
      INSERT INTO docker_containers (name, image, state, status, ports, environment, volumes)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      ON CONFLICT (name) 
      DO UPDATE SET 
        image = EXCLUDED.image,
        state = EXCLUDED.state,
        status = EXCLUDED.status,
        ports = EXCLUDED.ports,
        environment = EXCLUDED.environment,
        volumes = EXCLUDED.volumes,
        last_seen = NOW()
      RETURNING *
    `
    const values = [
      containerData.name,
      containerData.image,
      containerData.state,
      containerData.status,
      JSON.stringify(containerData.ports),
      JSON.stringify(containerData.environment),
      JSON.stringify(containerData.volumes)
    ]
    const result = await db.query(query, values)
    return result.rows[0]
  }

  async getContainers() {
    const query = 'SELECT * FROM docker_containers ORDER BY name'
    const result = await db.query(query)
    return result.rows
  }

  // System metrics
  async recordMetric(metricData: {
    type: string
    name: string
    value: number
    unit?: string
    metadata?: any
  }) {
    const query = `
      INSERT INTO system_metrics (metric_type, metric_name, value, unit, metadata)
      VALUES ($1, $2, $3, $4, $5)
    `
    const values = [
      metricData.type,
      metricData.name,
      metricData.value,
      metricData.unit,
      JSON.stringify(metricData.metadata)
    ]
    await db.query(query, values)
  }

  // Audit logging
  async logAction(logData: {
    userId?: string
    action: string
    resourceType?: string
    resourceId?: string
    details?: any
    ipAddress?: string
    userAgent?: string
  }) {
    const query = `
      INSERT INTO audit_logs (user_id, action, resource_type, resource_id, details, ip_address, user_agent)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
    `
    const values = [
      logData.userId,
      logData.action,
      logData.resourceType,
      logData.resourceId,
      JSON.stringify(logData.details),
      logData.ipAddress,
      logData.userAgent
    ]
    await db.query(query, values)
  }
}

export const dbService = new DatabaseService()
```

### 4. Hono Routes with Database Integration
```typescript
// packages/server/src/routes/users.ts
import { Hono } from 'hono'
import { dbService } from '../services/database'
import { authMiddleware } from '../middleware/auth'

const userRoutes = new Hono()

userRoutes.use('*', authMiddleware)

userRoutes.get('/profile', async (c) => {
  const user = c.get('user')
  const keycloakId = user.sub
  
  try {
    const userProfile = await dbService.getUserByKeycloakId(keycloakId)
    
    if (!userProfile) {
      // Create user if doesn't exist
      const newUser = await dbService.createUser({
        keycloakId,
        username: user.preferred_username,
        email: user.email,
        firstName: user.given_name,
        lastName: user.family_name
      })
      return c.json(newUser)
    }
    
    // Update last login
    await dbService.updateUserLastLogin(userProfile.id)
    
    return c.json(userProfile)
  } catch (error) {
    console.error('Error fetching user profile:', error)
    return c.json({ error: 'Failed to fetch user profile' }, 500)
  }
})

export { userRoutes }
```

## Database Monitoring and Maintenance

### 1. Performance Monitoring Queries
```sql
-- Slow queries
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;

-- Database connections
SELECT 
    datname,
    numbackends,
    xact_commit,
    xact_rollback
FROM pg_stat_database;

-- Lock monitoring
SELECT 
    blocked_locks.pid AS blocked_pid,
    blocked_activity.usename AS blocked_user,
    blocking_locks.pid AS blocking_pid,
    blocking_activity.usename AS blocking_user,
    blocked_activity.query AS blocked_statement,
    blocking_activity.query AS current_statement_in_blocking_process
FROM pg_catalog.pg_locks blocked_locks
JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
JOIN pg_catalog.pg_locks blocking_locks ON blocking_locks.locktype = blocked_locks.locktype
JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
WHERE NOT blocked_locks.granted;
```

### 2. Backup and Recovery
```bash
# Create backup
pg_dump -h localhost -U unraid_user -d unraid_db > backup_$(date +%Y%m%d_%H%M%S).sql

# Restore from backup
psql -h localhost -U unraid_user -d unraid_db < backup_20231201_120000.sql

# Automated backup script
#!/bin/bash
BACKUP_DIR="/mnt/tank/backups/postgres"
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump -h localhost -U unraid_user -d unraid_db | gzip > "$BACKUP_DIR/unraid_db_$DATE.sql.gz"

# Keep only last 7 days of backups
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete
```

## Best Practices

1. **Use Connection Pooling**: Implement proper connection pooling
2. **Parameterized Queries**: Always use parameterized queries to prevent SQL injection
3. **Indexing**: Create appropriate indexes for query performance
4. **Regular Maintenance**: Schedule regular VACUUM and ANALYZE operations
5. **Monitoring**: Monitor query performance and database metrics
6. **Backups**: Implement automated backup strategies
7. **Security**: Use strong passwords and limit database access
8. **Transactions**: Use transactions for data consistency
