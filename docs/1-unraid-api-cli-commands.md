# Unraid API CLI Commands Reference

## Overview
Unraid provides both a web interface and API endpoints for managing your server. This document covers useful CLI commands and API interactions for your Unraid Control Panel project.

## Unraid CLI Commands

### Docker Container Management
```bash
# List all Docker containers
docker ps -a

# Start a container
docker start <container_name>

# Stop a container
docker stop <container_name>

# Restart a container
docker restart <container_name>

# View container logs
docker logs <container_name>

# Execute commands in running container
docker exec -it <container_name> /bin/bash
```

### System Information
```bash
# Check system status
/usr/local/emhttp/webGui/scripts/notify -e "System Status" -s "System is running"

# View disk usage
df -h

# Check memory usage
free -h

# View CPU information
cat /proc/cpuinfo

# Check system uptime
uptime

# View running processes
ps aux
```

### Array Management
```bash
# Start the array
/usr/local/sbin/emcmd cmdStart=Start

# Stop the array
/usr/local/sbin/emcmd cmdStart=Stop

# Check array status
cat /var/local/emhttp/var.ini | grep mdState
```

### Network Commands
```bash
# View network interfaces
ip addr show

# Check network connectivity
ping google.com

# View routing table
ip route show

# Check open ports
netstat -tulpn
```

## Unraid GraphQL API

### API Endpoint
- **URL**: `http://your-unraid-ip/graphql`
- **Authentication**: Session-based (requires login)

### Common GraphQL Queries

#### Get Docker Containers
```graphql
query GetDockers {
  dockers {
    name
    state
    image
    ports
    created
    status
  }
}
```

#### Get System Information
```graphql
query GetSystemInfo {
  system {
    version
    uptime
    memory {
      total
      used
      free
    }
    cpu {
      usage
      temperature
    }
  }
}
```

#### Get Array Status
```graphql
query GetArrayStatus {
  array {
    state
    disks {
      name
      device
      size
      used
      free
      temperature
    }
  }
}
```

### GraphQL Mutations

#### Start Docker Container
```graphql
mutation StartDocker($name: String!) {
  startDocker(name: $name) {
    success
    message
  }
}
```

#### Stop Docker Container
```graphql
mutation StopDocker($name: String!) {
  stopDocker(name: $name) {
    success
    message
  }
}
```

## Useful API Endpoints for Your Project

### Authentication
- **Login**: `POST /login`
- **Logout**: `POST /logout`
- **Session Check**: `GET /webGui/include/CheckSession.php`

### Docker Management
- **Container List**: `GET /plugins/dynamix.docker.manager/include/DockerContainers.php`
- **Container Control**: `POST /plugins/dynamix.docker.manager/include/DockerUpdate.php`

### System Monitoring
- **Dashboard Data**: `GET /webGui/include/Dashboard.php`
- **System Stats**: `GET /webGui/include/SystemStats.php`

## Integration with Your Hono Backend

### Example Hono Route for Docker Management
```typescript
import { Hono } from 'hono'
import { request } from 'graphql-request'

const app = new Hono()

app.get('/api/containers', async (c) => {
  const query = `
    query GetDockers {
      dockers {
        name
        state
        image
        ports
      }
    }
  `
  
  try {
    const data = await request('http://your-unraid-ip/graphql', query)
    return c.json(data)
  } catch (error) {
    return c.json({ error: 'Failed to fetch containers' }, 500)
  }
})

app.post('/api/containers/:name/start', async (c) => {
  const name = c.req.param('name')
  const mutation = `
    mutation StartDocker($name: String!) {
      startDocker(name: $name) {
        success
        message
      }
    }
  `
  
  try {
    const data = await request('http://your-unraid-ip/graphql', mutation, { name })
    return c.json(data)
  } catch (error) {
    return c.json({ error: 'Failed to start container' }, 500)
  }
})
```

## Security Considerations

1. **API Authentication**: Always authenticate requests to Unraid API
2. **CORS**: Configure CORS properly for your frontend
3. **Rate Limiting**: Implement rate limiting to prevent API abuse
4. **Input Validation**: Validate all inputs before sending to Unraid API
5. **Error Handling**: Implement proper error handling for API failures

## Monitoring and Logging

### Log Locations
- **System Log**: `/var/log/syslog`
- **Docker Logs**: `/var/lib/docker/containers/`
- **Unraid Logs**: `/var/log/`

### Useful Monitoring Commands
```bash
# Monitor system resources
htop

# Watch log files
tail -f /var/log/syslog

# Monitor network traffic
iftop

# Check disk I/O
iotop
```

## Next Steps

1. Set up authentication with Keycloak
2. Create GraphQL client in your Hono backend
3. Implement container management endpoints
4. Add real-time updates using WebSockets
5. Create responsive UI components in SvelteKit
