# Unraid API CLI Commands Reference

## Overview
Unraid provides a modern GraphQL API for managing your server. This document covers the official Unraid API CLI commands and GraphQL interactions for your Unraid Control Panel project.

## Unraid API Service Management

### Start API Service
```bash
# Start the Unraid API service
unraid-api start

# Start with specific log level
unraid-api start --log-level debug
unraid-api start --log-level trace
```

### Stop API Service
```bash
# Stop the Unraid API service
unraid-api stop

# Stop and delete PM2 home directory
unraid-api stop --delete
```

### Restart API Service
```bash
# Restart the Unraid API service
unraid-api restart
```

### View API Logs
```bash
# View API logs (default: 100 lines)
unraid-api logs

# View specific number of lines
unraid-api logs -l 50
unraid-api logs --lines 200
```

## Configuration Commands

### View Current Configuration
```bash
# Display current configuration values
unraid-api config
```

### Environment Management
```bash
# Switch between environments (interactive)
unraid-api switch-env

# Switch to specific environment
unraid-api switch-env -e production
unraid-api switch-env -e staging
unraid-api switch-env --environment production
```

### Developer Mode
```bash
# Interactive developer tools prompt
unraid-api developer

# Enable GraphQL sandbox at /graphql
unraid-api developer --sandbox true

# Disable GraphQL sandbox
unraid-api developer --sandbox false

# Enable modal testing tool
unraid-api developer --enable-modal

# Disable modal testing tool
unraid-api developer --disable-modal
```

## API Key Management

### Create API Key
```bash
# Create a new API key (interactive)
unraid-api apikey --create

# Create API key with specific parameters
unraid-api apikey --name "My App Key" --create --roles admin --permissions read,write --description "API key for my application"
```

### API Key Options
- `--name <name>`: Name of the key
- `--create`: Create a new key
- `-r, --roles <roles>`: Comma-separated list of roles (admin, connect, guest)
- `-p, --permissions <permissions>`: Comma-separated list of permissions
- `-d, --description <description>`: Description for the key

## SSO (Single Sign-On) Management

### Add SSO User
```bash
# Add SSO user (interactive)
unraid-api sso add-user
unraid-api sso add
unraid-api sso a
```

### Remove SSO User
```bash
# Remove SSO user (interactive)
unraid-api sso remove-user
unraid-api sso remove
unraid-api sso r
```

### List SSO Users
```bash
# List all configured SSO users
unraid-api sso list-users
unraid-api sso list
unraid-api sso l
```

### Validate SSO Token
```bash
# Validate an SSO token
unraid-api sso validate-token <token>
unraid-api sso validate <token>
unraid-api sso v <token>
```

## Report Generation

### Generate System Report
```bash
# Generate system report
unraid-api report

# Display raw command output
unraid-api report -r
unraid-api report --raw

# Display output in JSON format
unraid-api report -j
unraid-api report --json
```

## Unraid GraphQL API

### API Endpoint and Authentication
- **URL**: `http://your-unraid-ip/graphql`
- **GraphQL Sandbox**: `http://your-unraid-ip/graphql` (when developer mode enabled)
- **Authentication Methods**:
  - API Keys (recommended for applications)
  - Cookies (when signed into WebGUI)

### Authentication Headers
```bash
# Using API Key
curl -H "x-api-key: YOUR_API_KEY" \
     -H "Content-Type: application/json" \
     -d '{"query": "{ info { os { platform } } }"}' \
     http://your-unraid-ip/graphql
```

### Core GraphQL Queries

#### System Information
```graphql
query SystemInfo {
  info {
    os {
      platform
      distro
      release
      uptime
    }
    cpu {
      manufacturer
      brand
      cores
      threads
    }
  }
}
```

#### Array Status and Management
```graphql
query ArrayStatus {
  array {
    state
    capacity {
      disks {
        free
        used
        total
      }
    }
    disks {
      name
      size
      status
      temp
    }
  }
}
```

#### Docker Container Management
```graphql
query DockerContainers {
  dockerContainers {
    id
    names
    state
    status
    autoStart
  }
}
```

### Available Schema Types

#### Base Types
- `Node`: Interface for objects with unique IDs
- `JSON`: For complex JSON data
- `DateTime`: For timestamp values
- `Long`: For 64-bit integers

#### Resource Types
- `Array`: Array and disk management
- `Docker`: Container and network management
- `Info`: System information
- `Config`: Server configuration
- `Connect`: Remote access settings

#### Role-Based Access
- `admin`: Full access to all operations
- `connect`: Remote access features
- `guest`: Limited read access

## Integration with Your Hono Backend

### GraphQL Client Setup
```typescript
import { GraphQLClient } from 'graphql-request'

const client = new GraphQLClient('http://your-unraid-ip/graphql', {
  headers: {
    'x-api-key': process.env.UNRAID_API_KEY || '',
  },
})
```

### Example Hono Routes with Unraid API
```typescript
import { Hono } from 'hono'
import { GraphQLClient } from 'graphql-request'

const app = new Hono()
const unraidClient = new GraphQLClient('http://your-unraid-ip/graphql', {
  headers: {
    'x-api-key': process.env.UNRAID_API_KEY || '',
  },
})

// Get Docker containers
app.get('/api/containers', async (c) => {
  const query = `
    query DockerContainers {
      dockerContainers {
        id
        names
        state
        status
        autoStart
      }
    }
  `

  try {
    const data = await unraidClient.request(query)
    return c.json(data)
  } catch (error) {
    console.error('Failed to fetch containers:', error)
    return c.json({ error: 'Failed to fetch containers' }, 500)
  }
})

// Get system information
app.get('/api/system', async (c) => {
  const query = `
    query SystemInfo {
      info {
        os {
          platform
          distro
          release
          uptime
        }
        cpu {
          manufacturer
          brand
          cores
          threads
        }
      }
    }
  `

  try {
    const data = await unraidClient.request(query)
    return c.json(data)
  } catch (error) {
    console.error('Failed to fetch system info:', error)
    return c.json({ error: 'Failed to fetch system information' }, 500)
  }
})

// Get array status
app.get('/api/array', async (c) => {
  const query = `
    query ArrayStatus {
      array {
        state
        capacity {
          disks {
            free
            used
            total
          }
        }
        disks {
          name
          size
          status
          temp
        }
      }
    }
  `

  try {
    const data = await unraidClient.request(query)
    return c.json(data)
  } catch (error) {
    console.error('Failed to fetch array status:', error)
    return c.json({ error: 'Failed to fetch array status' }, 500)
  }
})
```

## Security and Best Practices

### API Key Security
1. **Store API keys securely**: Use environment variables, never commit to code
2. **Use appropriate roles**: Assign minimal required permissions
3. **Rotate keys regularly**: Generate new keys periodically
4. **Monitor API usage**: Track API key usage and detect anomalies

### Rate Limiting and Error Handling
```typescript
// Example rate limiting and error handling
import { Hono } from 'hono'
import { rateLimiter } from 'hono/rate-limiter'

const app = new Hono()

// Apply rate limiting
app.use('/api/*', rateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  limit: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP'
}))

// Error handling middleware
app.onError((err, c) => {
  console.error('API Error:', err)

  if (err.message.includes('rate limit')) {
    return c.json({ error: 'Rate limit exceeded' }, 429)
  }

  if (err.message.includes('authentication')) {
    return c.json({ error: 'Authentication failed' }, 401)
  }

  return c.json({ error: 'Internal server error' }, 500)
})
```

### GraphQL Best Practices
1. **Query complexity**: Monitor and limit query complexity
2. **Field selection**: Only request needed fields
3. **Caching**: Implement appropriate caching strategies
4. **Pagination**: Use pagination for large datasets

## Environment Configuration

### Required Environment Variables
```env
# Unraid API Configuration
UNRAID_HOST=*************
UNRAID_API_KEY=your_api_key_here
UNRAID_GRAPHQL_ENDPOINT=http://*************/graphql

# Optional: API Configuration
UNRAID_API_TIMEOUT=30000
UNRAID_API_RETRIES=3
```

### Development vs Production
```typescript
// config/unraid.ts
export const unraidConfig = {
  host: process.env.UNRAID_HOST || 'localhost',
  apiKey: process.env.UNRAID_API_KEY,
  endpoint: process.env.UNRAID_GRAPHQL_ENDPOINT || `http://${process.env.UNRAID_HOST}/graphql`,
  timeout: parseInt(process.env.UNRAID_API_TIMEOUT || '30000'),
  retries: parseInt(process.env.UNRAID_API_RETRIES || '3'),
}
```

## Monitoring and Troubleshooting

### API Health Monitoring
```typescript
// Health check endpoint
app.get('/health/unraid', async (c) => {
  try {
    const query = `query { info { os { platform } } }`
    await unraidClient.request(query)
    return c.json({ status: 'healthy', timestamp: new Date().toISOString() })
  } catch (error) {
    return c.json({
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString()
    }, 503)
  }
})
```

### Logging and Debugging
```bash
# Check Unraid API service status
unraid-api logs

# Monitor API requests (if logging enabled)
tail -f /var/log/unraid-api.log

# Debug GraphQL queries in sandbox
# Navigate to http://your-unraid-ip/graphql
```

## Important Notes

1. **Permissions**: Most commands require appropriate permissions to modify system state
2. **Service Dependencies**: Some commands require the API to be running or stopped
3. **API Key Storage**: Store API keys securely as they provide system access
4. **SSO Changes**: SSO configuration changes may require a service restart
5. **Rate Limits**: The API implements rate limiting to prevent abuse

## Next Steps for Your Project

1. **Enable GraphQL Sandbox**: `unraid-api developer --sandbox true`
2. **Create API Key**: `unraid-api apikey --create`
3. **Set up Hono backend** with GraphQL client
4. **Implement authentication** with Keycloak
5. **Create SvelteKit frontend** with real-time updates
6. **Deploy to TrueNAS** with Docker containers