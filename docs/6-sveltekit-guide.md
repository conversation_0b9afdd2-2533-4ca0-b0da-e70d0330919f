# SvelteKit Guide for Unraid Control Panel

## What is SvelteKit?

SvelteKit is a framework for building web applications of all sizes, with a beautiful development experience and flexible filesystem-based routing. It's built on top of Svelte, a compile-time framework that generates vanilla JavaScript.

### Key Features
- **File-based Routing**: Automatic routing based on file structure
- **Server-Side Rendering (SSR)**: Built-in SSR with hydration
- **Static Site Generation (SSG)**: Pre-render pages at build time
- **API Routes**: Built-in API endpoint support
- **TypeScript Support**: First-class TypeScript support
- **Hot Module Replacement**: Fast development with HMR
- **Adapters**: Deploy anywhere with various adapters

### Why SvelteKit for Your Project?
1. **Performance**: Compiled output with minimal runtime overhead
2. **Developer Experience**: Excellent tooling and development server
3. **Flexibility**: SSR, SSG, or SPA modes
4. **Modern**: Built-in support for modern web standards
5. **Small Bundle Size**: Efficient code generation
6. **TypeScript**: Excellent TypeScript integration

## Project Setup

### 1. SvelteKit Setup (No separate init needed)
Since you're using a single package.json, SvelteKit dependencies are already included in the root package.json. You just need to set up the configuration files.

### 2. Client TypeScript Configuration
```json
// client/tsconfig.json
{
  "extends": "../tsconfig.json",
  "compilerOptions": {
    "allowJs": true,
    "checkJs": true,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "skipLibCheck": true,
    "sourceMap": true,
    "strict": true,
    "moduleResolution": "bundler",
    "paths": {
      "$app/*": [".svelte-kit/ambient.d.ts"],
      "$lib": ["./src/lib"],
      "$lib/*": ["./src/lib/*"],
      "@shared/*": ["../shared/src/*"]
    }
  },
  "include": [
    "ambient.d.ts",
    "./types/**/$types.d.ts",
    "./src/**/*.js",
    "./src/**/*.ts",
    "./src/**/*.svelte",
    "./tests/**/*.js",
    "./tests/**/*.ts",
    "./tests/**/*.svelte"
  ],
  "exclude": ["../node_modules/**", ".svelte-kit/**"]
}
```

## Project Structure

### File-based Routing Structure
```
client/
├── src/
│   ├── app.html                 # HTML template
│   ├── app.d.ts                 # Global type definitions
│   ├── hooks.client.ts          # Client-side hooks
│   ├── hooks.server.ts          # Server-side hooks
│   ├── lib/                     # Shared utilities and components
│   │   ├── components/          # Reusable components
│   │   │   ├── ui/              # UI components (shadcn-style)
│   │   │   ├── layout/          # Layout components
│   │   │   └── features/        # Feature-specific components
│   │   ├── stores/              # Svelte stores
│   │   ├── utils/               # Utility functions
│   │   ├── types/               # TypeScript types
│   │   └── api/                 # API client functions
│   ├── routes/                  # File-based routing
│   │   ├── +layout.svelte       # Root layout
│   │   ├── +layout.ts           # Root layout data
│   │   ├── +page.svelte         # Home page
│   │   ├── +page.ts             # Home page data
│   │   ├── auth/                # Authentication routes
│   │   │   ├── login/
│   │   │   └── callback/
│   │   ├── dashboard/           # Dashboard routes
│   │   │   ├── +page.svelte
│   │   │   ├── containers/
│   │   │   ├── system/
│   │   │   └── settings/
│   │   └── api/                 # API routes (if needed)
│   └── styles/                  # Global styles
├── static/                      # Static assets
├── tests/                       # Test files
├── vite.config.ts              # Vite configuration
├── svelte.config.js            # SvelteKit configuration
├── tailwind.config.js          # Tailwind configuration
└── tsconfig.json               # TypeScript configuration
```

## Core Configuration Files

### 1. SvelteKit Configuration (svelte.config.js)
```javascript
import adapter from '@sveltejs/adapter-auto'
import { vitePreprocess } from '@sveltejs/kit/vite'

/** @type {import('@sveltejs/kit').Config} */
const config = {
  preprocess: vitePreprocess(),
  kit: {
    adapter: adapter(),
    alias: {
      $components: 'src/lib/components',
      $stores: 'src/lib/stores',
      $utils: 'src/lib/utils',
      $types: 'src/lib/types',
      $api: 'src/lib/api'
    },
    env: {
      publicPrefix: 'PUBLIC_'
    }
  }
}

export default config
```

### 2. Vite Configuration (vite.config.ts)
```typescript
import { sveltekit } from '@sveltejs/kit/vite'
import { defineConfig } from 'vite'

export default defineConfig({
  plugins: [sveltekit()],
  server: {
    host: '0.0.0.0',
    port: 5173,
    proxy: {
      '/api': {
        target: 'http://localhost:3001',
        changeOrigin: true
      }
    }
  },
  build: {
    target: 'esnext'
  }
})
```

### 3. Tailwind Configuration (tailwind.config.js)
```javascript
/** @type {import('tailwindcss').Config} */
export default {
  content: ['./src/**/*.{html,js,svelte,ts}'],
  theme: {
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))"
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))"
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))"
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))"
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))"
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))"
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))"
        }
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)"
      }
    }
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography')
  ]
}
```

## Authentication Integration

### 1. Auth Configuration (src/hooks.server.ts)
```typescript
import { SvelteKitAuth } from '@auth/sveltekit'
import { env } from '$env/dynamic/private'

export const handle = SvelteKitAuth({
  providers: [
    {
      id: 'keycloak',
      name: 'Keycloak',
      type: 'oauth',
      authorization: {
        url: `${env.KEYCLOAK_URL}/realms/${env.KEYCLOAK_REALM}/protocol/openid-connect/auth`,
        params: {
          scope: 'openid email profile',
          response_type: 'code'
        }
      },
      token: `${env.KEYCLOAK_URL}/realms/${env.KEYCLOAK_REALM}/protocol/openid-connect/token`,
      userinfo: `${env.KEYCLOAK_URL}/realms/${env.KEYCLOAK_REALM}/protocol/openid-connect/userinfo`,
      clientId: env.KEYCLOAK_CLIENT_ID,
      clientSecret: env.KEYCLOAK_CLIENT_SECRET,
      profile(profile) {
        return {
          id: profile.sub,
          name: profile.name,
          email: profile.email,
          image: profile.picture
        }
      }
    }
  ],
  callbacks: {
    async jwt({ token, account }) {
      if (account) {
        token.accessToken = account.access_token
      }
      return token
    },
    async session({ session, token }) {
      session.accessToken = token.accessToken
      return session
    }
  }
})
```

### 2. Auth Store (src/lib/stores/auth.ts)
```typescript
import { writable } from 'svelte/store'
import { page } from '$app/stores'
import { derived } from 'svelte/store'

export interface User {
  id: string
  name: string
  email: string
  image?: string
}

export const user = writable<User | null>(null)
export const isAuthenticated = derived(user, ($user) => !!$user)

export const authStore = {
  subscribe: user.subscribe,
  set: user.set,
  update: user.update,
  login: () => {
    // Redirect to auth provider
    window.location.href = '/auth/signin'
  },
  logout: () => {
    // Clear user and redirect
    user.set(null)
    window.location.href = '/auth/signout'
  }
}
```

## API Client Integration

### 1. API Client (src/lib/api/client.ts)
```typescript
import { browser } from '$app/environment'
import { page } from '$app/stores'
import { get } from 'svelte/store'

class ApiClient {
  private baseUrl: string
  private token: string | null = null

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl
  }

  setToken(token: string) {
    this.token = token
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`
    
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers
    }

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`
    }

    const response = await fetch(url, {
      ...options,
      headers
    })

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`)
    }

    return response.json()
  }

  // Docker container methods
  async getContainers() {
    return this.request<any[]>('/api/docker')
  }

  async startContainer(name: string) {
    return this.request(`/api/docker/${name}/start`, {
      method: 'POST'
    })
  }

  async stopContainer(name: string) {
    return this.request(`/api/docker/${name}/stop`, {
      method: 'POST'
    })
  }

  async restartContainer(name: string) {
    return this.request(`/api/docker/${name}/restart`, {
      method: 'POST'
    })
  }

  // System methods
  async getSystemInfo() {
    return this.request('/api/system/info')
  }

  async getSystemMetrics() {
    return this.request('/api/system/metrics')
  }
}

export const apiClient = new ApiClient(
  browser ? 'http://localhost:3001' : 'http://localhost:3001'
)
```

### 2. API Stores (src/lib/stores/containers.ts)
```typescript
import { writable, derived } from 'svelte/store'
import { apiClient } from '$api/client'
import type { DockerContainer } from '@reza-unraid/shared'

export const containers = writable<DockerContainer[]>([])
export const loading = writable(false)
export const error = writable<string | null>(null)

export const runningContainers = derived(
  containers,
  ($containers) => $containers.filter(c => c.state === 'running')
)

export const stoppedContainers = derived(
  containers,
  ($containers) => $containers.filter(c => c.state === 'stopped')
)

export const containerStore = {
  subscribe: containers.subscribe,
  
  async load() {
    loading.set(true)
    error.set(null)
    
    try {
      const data = await apiClient.getContainers()
      containers.set(data)
    } catch (err) {
      error.set(err instanceof Error ? err.message : 'Failed to load containers')
    } finally {
      loading.set(false)
    }
  },

  async startContainer(name: string) {
    try {
      await apiClient.startContainer(name)
      // Reload containers to get updated state
      await this.load()
    } catch (err) {
      error.set(err instanceof Error ? err.message : 'Failed to start container')
    }
  },

  async stopContainer(name: string) {
    try {
      await apiClient.stopContainer(name)
      await this.load()
    } catch (err) {
      error.set(err instanceof Error ? err.message : 'Failed to stop container')
    }
  }
}
```

## Component Examples

### 1. Container Card Component (src/lib/components/ContainerCard.svelte)
```svelte
<script lang="ts">
  import { Play, Square, RotateCcw } from 'lucide-svelte'
  import { containerStore } from '$stores/containers'
  import type { DockerContainer } from '@reza-unraid/shared'

  export let container: DockerContainer

  $: isRunning = container.state === 'running'
  $: statusColor = isRunning ? 'text-green-600' : 'text-red-600'

  async function handleStart() {
    await containerStore.startContainer(container.name)
  }

  async function handleStop() {
    await containerStore.stopContainer(container.name)
  }
</script>

<div class="bg-white rounded-lg shadow-md p-6 border border-gray-200">
  <div class="flex items-center justify-between mb-4">
    <h3 class="text-lg font-semibold text-gray-900">{container.name}</h3>
    <span class="px-2 py-1 text-xs font-medium rounded-full {statusColor} bg-gray-100">
      {container.state}
    </span>
  </div>

  <div class="space-y-2 mb-4">
    <p class="text-sm text-gray-600">
      <span class="font-medium">Image:</span> {container.image}
    </p>
    <p class="text-sm text-gray-600">
      <span class="font-medium">Status:</span> {container.status}
    </p>
    {#if container.ports && container.ports.length > 0}
      <p class="text-sm text-gray-600">
        <span class="font-medium">Ports:</span> {container.ports.join(', ')}
      </p>
    {/if}
  </div>

  <div class="flex space-x-2">
    {#if isRunning}
      <button
        on:click={handleStop}
        class="flex items-center px-3 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
      >
        <Square class="w-4 h-4 mr-2" />
        Stop
      </button>
    {:else}
      <button
        on:click={handleStart}
        class="flex items-center px-3 py-2 text-sm font-medium text-white bg-green-600 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
      >
        <Play class="w-4 h-4 mr-2" />
        Start
      </button>
    {/if}
    
    <button
      class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500"
    >
      <RotateCcw class="w-4 h-4 mr-2" />
      Restart
    </button>
  </div>
</div>
```

### 2. Dashboard Layout (src/routes/dashboard/+layout.svelte)
```svelte
<script lang="ts">
  import { page } from '$app/stores'
  import { Container, Server, Settings, LogOut } from 'lucide-svelte'
  import { authStore } from '$stores/auth'

  const navigation = [
    { name: 'Containers', href: '/dashboard/containers', icon: Container },
    { name: 'System', href: '/dashboard/system', icon: Server },
    { name: 'Settings', href: '/dashboard/settings', icon: Settings }
  ]

  $: currentPath = $page.url.pathname
</script>

<div class="min-h-screen bg-gray-50">
  <!-- Sidebar -->
  <div class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg">
    <div class="flex flex-col h-full">
      <!-- Logo -->
      <div class="flex items-center px-6 py-4 border-b border-gray-200">
        <h1 class="text-xl font-bold text-gray-900">Unraid Control</h1>
      </div>

      <!-- Navigation -->
      <nav class="flex-1 px-4 py-6 space-y-2">
        {#each navigation as item}
          <a
            href={item.href}
            class="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors
              {currentPath.startsWith(item.href) 
                ? 'bg-blue-100 text-blue-700' 
                : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'}"
          >
            <svelte:component this={item.icon} class="w-5 h-5 mr-3" />
            {item.name}
          </a>
        {/each}
      </nav>

      <!-- User menu -->
      <div class="px-4 py-4 border-t border-gray-200">
        <button
          on:click={authStore.logout}
          class="flex items-center w-full px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:bg-gray-100 hover:text-gray-900"
        >
          <LogOut class="w-5 h-5 mr-3" />
          Sign out
        </button>
      </div>
    </div>
  </div>

  <!-- Main content -->
  <div class="pl-64">
    <main class="p-8">
      <slot />
    </main>
  </div>
</div>
```

## Development Workflow

### 1. Development Commands
```bash
# Start client development server
bun run dev:client

# Start both client and server
bun run dev

# Build client for production
bun run build:client

# Build everything
bun run build

# Type checking
bun run type-check

# Linting and formatting
bun run lint
bun run format
```

### 2. Environment Variables
```env
# .env
PUBLIC_API_URL=http://localhost:3001
KEYCLOAK_URL=http://localhost:8080
KEYCLOAK_REALM=unraid-realm
KEYCLOAK_CLIENT_ID=unraid-client
KEYCLOAK_CLIENT_SECRET=your_client_secret
```

## Best Practices

1. **Component Organization**: Keep components small and focused
2. **State Management**: Use stores for shared state
3. **Type Safety**: Leverage TypeScript throughout
4. **Performance**: Use reactive statements efficiently
5. **Accessibility**: Include proper ARIA attributes
6. **Error Handling**: Implement proper error boundaries
7. **Testing**: Write unit and integration tests
8. **SEO**: Use proper meta tags and structured data